
window.onload = function() {
  // Build a system
  let url = window.location.search.match(/url=([^&]+)/);
  if (url && url.length > 1) {
    url = decodeURIComponent(url[1]);
  } else {
    url = window.location.origin;
  }
  let options = {
  "swaggerDoc": {
    "openapi": "3.0.0",
    "paths": {
      "/v1/sms/users/me": {
        "get": {
          "description": "\n        Retrieves the current authenticated user's profile information.\n        The response structure varies based on the user's role:\n\n        **Role-based Response Types:**\n        - **General Users**: Basic user profile with standard fields\n        - **Institute Owners**: User profile extended with setup completion status\n        - **Teachers**: Staff profile extended with teaching assignments and class information\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Response Variations:**\n        - Institute owners receive additional `hasCompletedSetup` field\n        - Teachers receive additional `classTeacherOf` and `subjectTeacherOf` arrays\n        - Other users receive the base user profile structure\n\n        **Teaching Assignment Details (Teachers only):**\n        - `classTeacherOf`: Array of class sections where the teacher is the class teacher\n        - `subjectTeacherOf`: Array of subject teaching assignments with class, section, and subject details\n      ",
          "operationId": "getCurrentUserProfile[0]",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Successfully retrieved user profile",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 200
                      },
                      "message": {
                        "type": "string",
                        "example": "Profile retrieved successfully"
                      },
                      "data": {
                        "oneOf": [
                          {
                            "$ref": "#/components/schemas/BaseUserProfileResponseDto",
                            "description": "Base user profile for general users"
                          },
                          {
                            "$ref": "#/components/schemas/InstituteOwnerProfileResponseDto",
                            "description": "Institute owner profile with setup status"
                          },
                          {
                            "$ref": "#/components/schemas/TeacherProfileResponseDto",
                            "description": "Teacher profile with teaching assignments"
                          }
                        ],
                        "discriminator": {
                          "propertyName": "role",
                          "mapping": {
                            "3000": "#/components/schemas/InstituteOwnerProfileResponseDto",
                            "4000": "#/components/schemas/TeacherProfileResponseDto"
                          }
                        }
                      }
                    }
                  },
                  "examples": {
                    "institute-owner": {
                      "summary": "Institute Owner Profile",
                      "description": "Example response for an institute owner user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "123e4567-e89b-12d3-a456-426614174000",
                          "name": "John Doe",
                          "email": "<EMAIL>",
                          "phone": "+1234567890",
                          "address": "123 Main St, City, Country",
                          "gender": "MALE",
                          "cnic": "12345-6789012-3",
                          "photo": "https://example.com/photo.jpg",
                          "role": 3000,
                          "isActive": true,
                          "isPasswordTemporary": false,
                          "createdAt": "2024-01-15T10:30:00Z",
                          "hasCompletedSetup": true
                        }
                      }
                    },
                    "teacher": {
                      "summary": "Teacher Profile",
                      "description": "Example response for a teacher user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "456e7890-e89b-12d3-a456-426614174001",
                          "name": "Jane Smith",
                          "email": "<EMAIL>",
                          "phone": "+1234567891",
                          "address": "456 School Ave, City, Country",
                          "gender": "FEMALE",
                          "cnic": "12345-6789012-4",
                          "photo": "https://example.com/teacher-photo.jpg",
                          "designation": "Mathematics Teacher",
                          "department": "ACADEMIC",
                          "type": "TEACHER",
                          "salary": 50000,
                          "createdAt": "2024-01-15T10:30:00Z",
                          "branchId": "789e0123-e89b-12d3-a456-426614174002",
                          "classTeacherOf": [
                            {
                              "id": "section-123",
                              "name": "Section A",
                              "totalStudents": 30,
                              "class": {
                                "id": "class-456",
                                "name": "Grade 10"
                              },
                              "createdAt": "2024-01-15T10:30:00Z"
                            }
                          ],
                          "subjectTeacherOf": [
                            {
                              "id": "assignment-789",
                              "academicSessionId": "session-2024",
                              "class": {
                                "id": "class-456",
                                "name": "Grade 10"
                              },
                              "section": {
                                "id": "section-123",
                                "name": "Section A",
                                "totalStudents": 30
                              },
                              "subject": {
                                "id": "subject-math",
                                "name": "Mathematics"
                              },
                              "createdAt": "2024-01-15T10:30:00Z"
                            }
                          ]
                        }
                      }
                    },
                    "general-user": {
                      "summary": "General User Profile",
                      "description": "Example response for a general user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "789e0123-e89b-12d3-a456-426614174003",
                          "name": "Bob Johnson",
                          "email": "<EMAIL>",
                          "phone": "+1234567892",
                          "address": "789 User St, City, Country",
                          "gender": "MALE",
                          "cnic": "12345-6789012-5",
                          "photo": null,
                          "role": 5000,
                          "isActive": true,
                          "isPasswordTemporary": false,
                          "createdAt": "2024-01-15T10:30:00Z"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get Current User Profile",
          "tags": [
            "Users"
          ]
        }
      },
      "/v1/platform/users/me": {
        "get": {
          "description": "\n        Retrieves the current authenticated user's profile information.\n        The response structure varies based on the user's role:\n\n        **Role-based Response Types:**\n        - **General Users**: Basic user profile with standard fields\n        - **Institute Owners**: User profile extended with setup completion status\n        - **Teachers**: Staff profile extended with teaching assignments and class information\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Response Variations:**\n        - Institute owners receive additional `hasCompletedSetup` field\n        - Teachers receive additional `classTeacherOf` and `subjectTeacherOf` arrays\n        - Other users receive the base user profile structure\n\n        **Teaching Assignment Details (Teachers only):**\n        - `classTeacherOf`: Array of class sections where the teacher is the class teacher\n        - `subjectTeacherOf`: Array of subject teaching assignments with class, section, and subject details\n      ",
          "operationId": "getCurrentUserProfile[1]",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Successfully retrieved user profile",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 200
                      },
                      "message": {
                        "type": "string",
                        "example": "Profile retrieved successfully"
                      },
                      "data": {
                        "oneOf": [
                          {
                            "$ref": "#/components/schemas/BaseUserProfileResponseDto",
                            "description": "Base user profile for general users"
                          },
                          {
                            "$ref": "#/components/schemas/InstituteOwnerProfileResponseDto",
                            "description": "Institute owner profile with setup status"
                          },
                          {
                            "$ref": "#/components/schemas/TeacherProfileResponseDto",
                            "description": "Teacher profile with teaching assignments"
                          }
                        ],
                        "discriminator": {
                          "propertyName": "role",
                          "mapping": {
                            "3000": "#/components/schemas/InstituteOwnerProfileResponseDto",
                            "4000": "#/components/schemas/TeacherProfileResponseDto"
                          }
                        }
                      }
                    }
                  },
                  "examples": {
                    "institute-owner": {
                      "summary": "Institute Owner Profile",
                      "description": "Example response for an institute owner user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "123e4567-e89b-12d3-a456-426614174000",
                          "name": "John Doe",
                          "email": "<EMAIL>",
                          "phone": "+1234567890",
                          "address": "123 Main St, City, Country",
                          "gender": "MALE",
                          "cnic": "12345-6789012-3",
                          "photo": "https://example.com/photo.jpg",
                          "role": 3000,
                          "isActive": true,
                          "isPasswordTemporary": false,
                          "createdAt": "2024-01-15T10:30:00Z",
                          "hasCompletedSetup": true
                        }
                      }
                    },
                    "teacher": {
                      "summary": "Teacher Profile",
                      "description": "Example response for a teacher user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "456e7890-e89b-12d3-a456-426614174001",
                          "name": "Jane Smith",
                          "email": "<EMAIL>",
                          "phone": "+1234567891",
                          "address": "456 School Ave, City, Country",
                          "gender": "FEMALE",
                          "cnic": "12345-6789012-4",
                          "photo": "https://example.com/teacher-photo.jpg",
                          "designation": "Mathematics Teacher",
                          "department": "ACADEMIC",
                          "type": "TEACHER",
                          "salary": 50000,
                          "createdAt": "2024-01-15T10:30:00Z",
                          "branchId": "789e0123-e89b-12d3-a456-426614174002",
                          "classTeacherOf": [
                            {
                              "id": "section-123",
                              "name": "Section A",
                              "totalStudents": 30,
                              "class": {
                                "id": "class-456",
                                "name": "Grade 10"
                              },
                              "createdAt": "2024-01-15T10:30:00Z"
                            }
                          ],
                          "subjectTeacherOf": [
                            {
                              "id": "assignment-789",
                              "academicSessionId": "session-2024",
                              "class": {
                                "id": "class-456",
                                "name": "Grade 10"
                              },
                              "section": {
                                "id": "section-123",
                                "name": "Section A",
                                "totalStudents": 30
                              },
                              "subject": {
                                "id": "subject-math",
                                "name": "Mathematics"
                              },
                              "createdAt": "2024-01-15T10:30:00Z"
                            }
                          ]
                        }
                      }
                    },
                    "general-user": {
                      "summary": "General User Profile",
                      "description": "Example response for a general user",
                      "value": {
                        "statusCode": 200,
                        "message": "Profile retrieved successfully",
                        "data": {
                          "id": "789e0123-e89b-12d3-a456-426614174003",
                          "name": "Bob Johnson",
                          "email": "<EMAIL>",
                          "phone": "+1234567892",
                          "address": "789 User St, City, Country",
                          "gender": "MALE",
                          "cnic": "12345-6789012-5",
                          "photo": null,
                          "role": 5000,
                          "isActive": true,
                          "isPasswordTemporary": false,
                          "createdAt": "2024-01-15T10:30:00Z"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get Current User Profile",
          "tags": [
            "Users"
          ]
        }
      },
      "/v1/platform/subscription-plans": {
        "get": {
          "description": "Returns a list of all available service packages with optional filtering and pagination",
          "operationId": "getAll",
          "parameters": [
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Successfully retrieved packages",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "data": {
                        "type": "array",
                        "items": {
                          "$ref": "#/components/schemas/PackageDto"
                        }
                      },
                      "meta": {
                        "type": "object",
                        "properties": {
                          "total": {
                            "type": "number",
                            "example": 42
                          },
                          "page": {
                            "type": "number",
                            "example": 1
                          },
                          "limit": {
                            "type": "number",
                            "example": 10
                          },
                          "pages": {
                            "type": "number",
                            "example": 5
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Retrieve all packages",
          "tags": [
            "Software Packages"
          ]
        },
        "post": {
          "description": "Creates a new service package with the provided details",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateSubscriptionPlanDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the created package with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PackageDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create a new package",
          "tags": [
            "Software Packages"
          ]
        }
      },
      "/v1/platform/subscription-plans/{id}": {
        "get": {
          "description": "Returns detailed information about a specific service package",
          "operationId": "getUnique",
          "parameters": [
            {
              "name": "id",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the package",
              "schema": {
                "example": "507f1f77bcf86cd799439011",
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Successfully retrieved package details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PackageDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Retrieve a package by ID",
          "tags": [
            "Software Packages"
          ]
        },
        "patch": {
          "description": "Updates a service package with the provided details",
          "operationId": "update",
          "parameters": [
            {
              "name": "id",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the package to update",
              "schema": {
                "example": "507f1f77bcf86cd799439011",
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "description": "Package update payload containing the fields to update",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PackageDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Successfully updated package with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PackageDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update an existing package",
          "tags": [
            "Software Packages"
          ]
        },
        "delete": {
          "description": "Permanently removes a service package from the system",
          "operationId": "delete",
          "parameters": [
            {
              "name": "id",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the package to delete",
              "schema": {
                "example": "507f1f77bcf86cd799439011",
                "type": "string"
              }
            }
          ],
          "responses": {
            "204": {
              "description": "Package successfully deleted"
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Delete a package",
          "tags": [
            "Software Packages"
          ]
        }
      },
      "/v1/sms/auth/login": {
        "post": {
          "description": "\n        Authenticates SMS system users including teachers, students, staff, guardians,\n        institute owners, and branch administrators. Returns an access token and user role.\n\n        **Important Notes:**\n        - Users with temporary passwords will only receive an access token (no refresh token)\n        - Users must change their temporary password using the set-password endpoint\n        - Refresh token is set as an HTTP-only cookie for security\n        - Access tokens expire in 15 minutes, refresh tokens in 30 minutes\n      ",
          "operationId": "login",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SignInDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Login successful - returns access token and user role",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/SmsLoginResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "401": {
              "description": "Unauthorized - Invalid credentials or session expired",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 401
                      },
                      "message": {
                        "type": "string",
                        "example": "Invalid email or password"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "SMS User Login",
          "tags": [
            "Authentication"
          ]
        }
      },
      "/v1/platform/auth/login": {
        "post": {
          "description": "\n        Authenticates platform administrators who manage the entire system.\n        Returns an access token, user role, and sets a refresh token cookie.\n\n        **Access Control:**\n        - Only users with PLATFORM_ADMIN role can use this endpoint\n        - Always returns a refresh token (no temporary password restrictions)\n        - Refresh token is automatically set as an HTTP-only cookie\n      ",
          "operationId": "platformLogin",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SignInDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Login successful - returns access token and user role",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlatformLoginResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "401": {
              "description": "Unauthorized - Invalid credentials",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 401
                      },
                      "message": {
                        "type": "string",
                        "example": "Invalid email or password"
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User is not a platform administrator",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Platform Admin Login",
          "tags": [
            "Authentication"
          ]
        }
      },
      "/v1/sms/auth/set-password": {
        "post": {
          "description": "\n        Allows authenticated users to set a new password, typically used to change\n        temporary passwords to permanent ones. This endpoint requires authentication.\n\n        **Usage Scenarios:**\n        - New users changing their temporary password\n        - Users updating their existing password\n        - Password reset completion\n\n        **Security Requirements:**\n        - Must be authenticated with a valid access token\n        - Password must meet complexity requirements (8+ chars, uppercase, lowercase, number, special char)\n        - Old temporary passwords are invalidated after successful change\n      ",
          "operationId": "setPassword",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SetPasswordDto"
                }
              }
            }
          },
          "responses": {
            "204": {
              "description": "Password updated successfully - no content returned"
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "401": {
              "description": "Unauthorized - Invalid or missing access token",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 401
                      },
                      "message": {
                        "type": "string",
                        "example": "Unauthorized"
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - User account not found",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "User not found"
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "security": [
            {
              "access-token": []
            }
          ],
          "summary": "Set User Password",
          "tags": [
            "Authentication"
          ]
        }
      },
      "/v1/sms/auth/refresh-token": {
        "post": {
          "description": "\n        Generates a new access token using the refresh token stored in HTTP-only cookies.\n        This endpoint extends user sessions without requiring re-authentication.\n\n        **Token Management:**\n        - Refresh token must be present in cookies (automatically sent by browser)\n        - Returns a new access token with the same permissions\n        - Refresh token remains valid until expiration\n        - Access tokens expire in 15 minutes, refresh tokens in 30 minutes\n\n        **Security Features:**\n        - Refresh tokens are HTTP-only cookies (not accessible via JavaScript)\n        - Automatic token rotation for enhanced security\n        - Validates token signature and expiration\n      ",
          "operationId": "refreshToken",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Token refresh successful - returns new access token and user role",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RefreshTokenResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "401": {
              "description": "Unauthorized - Missing, invalid, or expired refresh token",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 401
                      },
                      "message": {
                        "type": "string",
                        "example": "Invalid or expired refresh token"
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "security": [
            {
              "refreshToken": []
            }
          ],
          "summary": "Refresh Access Token",
          "tags": [
            "Authentication"
          ]
        }
      },
      "/v1/sms/upload/image": {
        "post": {
          "operationId": "uploadImage",
          "parameters": [],
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "File Uploads"
          ]
        }
      },
      "/v1/sms/upload/pdf": {
        "post": {
          "operationId": "uploadPdf",
          "parameters": [],
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "File Uploads"
          ]
        }
      },
      "/v1/sms/institute-owners": {
        "get": {
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Institute Owners"
          ]
        },
        "post": {
          "description": "Creates a new owner with the provided details",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateOwnerDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created owner with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/OwnerResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create a new Owner",
          "tags": [
            "Institute Owners"
          ]
        }
      },
      "/v1/sms/institute-owners/{id}": {
        "get": {
          "operationId": "get",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Institute Owners"
          ]
        }
      },
      "/v1/sms/institutes": {
        "get": {
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Institutes"
          ]
        },
        "post": {
          "description": "Creates a new institute with the provided details",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/createInstituteDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created institute with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/InstituteResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create a new Institute",
          "tags": [
            "Institutes"
          ]
        }
      },
      "/v1/sms/institutes/me": {
        "get": {
          "description": "Returns all institutes",
          "operationId": "getOwnerInstitute",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Returns the list of institutes",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/InstituteResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get all institutes",
          "tags": [
            "Institutes"
          ]
        },
        "patch": {
          "operationId": "updateOwnerInstitute",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateInstituteDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Institutes"
          ]
        }
      },
      "/v1/sms/institutes/{instituteId}": {
        "patch": {
          "operationId": "update",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateInstituteDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Institutes"
          ]
        }
      },
      "/v1/sms/institutes/{instituteId}/branches": {
        "get": {
          "description": "Returns all branches of the institute",
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Returns the list of branches",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/BranchResponseDto"
                    }
                  }
                }
              }
            }
          },
          "summary": "Get all branches",
          "tags": [
            "Institute Branches"
          ]
        },
        "post": {
          "description": "Creates a new branch with the provided details",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateBranchDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created branch with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BranchResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create a new Branch",
          "tags": [
            "Institute Branches"
          ]
        }
      },
      "/v1/sms/branches/{branchId}/sessions": {
        "post": {
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateAcademicSessionDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "Academic Sessions"
          ]
        },
        "get": {
          "operationId": "get",
          "parameters": [
            {
              "name": "branchId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Academic Sessions"
          ]
        }
      },
      "/v1/sms/branches/{branchId}/staff": {
        "post": {
          "description": "Creates a new staff with the provided details",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateStaffDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created staff with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/StaffProfileResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create a new Staff",
          "tags": [
            "Staff"
          ]
        },
        "get": {
          "description": "Returns all staff of the branch",
          "operationId": "get",
          "parameters": [
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of staff",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/StaffProfileResponseDto"
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get all staff",
          "tags": [
            "Staff"
          ]
        }
      },
      "/v1/sms/branches/{branchId}/staff/{staffId}": {
        "patch": {
          "operationId": "update",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateStaffDto"
                }
              }
            }
          },
          "responses": {
            "204": {
              "description": ""
            }
          },
          "tags": [
            "Staff"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/classes": {
        "get": {
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Classes"
          ]
        },
        "post": {
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateClassDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "Classes"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/classes/{classId}": {
        "patch": {
          "operationId": "update",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateClassDto"
                }
              }
            }
          },
          "responses": {
            "204": {
              "description": ""
            }
          },
          "tags": [
            "Classes"
          ]
        }
      },
      "/v1/sms/classes/{classId}": {
        "get": {
          "operationId": "findById",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Classes"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/students": {
        "post": {
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateStudentDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object"
                  }
                }
              }
            }
          },
          "tags": [
            "Students"
          ]
        },
        "get": {
          "operationId": "getAllSessionStudents",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Students"
          ]
        }
      },
      "/v1/sms/class-sections/{sectionId}/students": {
        "get": {
          "description": "Returns all students of the class section",
          "operationId": "getAllSectionStudents",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of students",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/StudentResponseDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get all students by class section ID",
          "tags": [
            "Students"
          ]
        }
      },
      "/v1/sms/classes/{classId}/sections": {
        "post": {
          "description": "Creates  a new class section ",
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateClassSectionDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the id of the newly created class section in the Location header",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 201
                      },
                      "message": {
                        "type": "string",
                        "example": "Class section created successfully"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Class Section",
          "tags": [
            "Class Sections"
          ]
        },
        "get": {
          "operationId": "getAllClassSections",
          "parameters": [
            {
              "name": "classId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Class Sections"
          ]
        }
      },
      "/v1/sms/classes/{classId}/sections/{sectionId}": {
        "get": {
          "operationId": "getClassSection",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Class Sections"
          ]
        }
      },
      "/v1/sms/sessions/{sessionId}/subjects": {
        "get": {
          "description": "\n        Retrieves all subjects for a specific academic session with pagination support.\n\n        **Features:**\n        - **Pagination**: Use `limit` and `offset` parameters to control the number of results\n        - **Ordering**: Results are ordered by creation date (most recent first)\n        - **Session-specific**: Only returns subjects for the specified academic session\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Permissions:** Only Institute Owners can view subjects.\n\n        **Use Cases:**\n        - Viewing all subjects configured for an academic session\n        - Managing curriculum and subject offerings\n        - Setting up class schedules and teacher assignments\n\n        **Response Structure:**\n        - Returns paginated list of subjects with complete details\n        - Includes total count for pagination\n        - Each subject includes: name, type, marks, active status, and timestamps\n      ",
          "operationId": "getAll",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the academic session",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of subjects to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of subjects to skip for pagination",
              "schema": {
                "example": 0,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Successfully retrieved subjects",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 200
                      },
                      "message": {
                        "type": "string",
                        "example": "Subjects retrieved successfully"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "items": {
                            "type": "array",
                            "items": {
                              "$ref": "#/components/schemas/SubjectResponseDto"
                            }
                          },
                          "total": {
                            "type": "number",
                            "example": 15
                          }
                        }
                      }
                    }
                  },
                  "examples": {
                    "subjects-list": {
                      "summary": "List of Subjects",
                      "description": "Example response with multiple subjects",
                      "value": {
                        "statusCode": 200,
                        "message": "Subjects retrieved successfully",
                        "data": {
                          "items": [
                            {
                              "id": "subject-123",
                              "name": "Mathematics",
                              "type": "THEORY",
                              "marks": 100,
                              "isActive": true,
                              "academicSessionId": "session-456",
                              "createdAt": "2024-01-15T10:30:00.000Z"
                            },
                            {
                              "id": "subject-456",
                              "name": "Physics Lab",
                              "type": "PRACTICAL",
                              "marks": 50,
                              "isActive": true,
                              "academicSessionId": "session-456",
                              "createdAt": "2024-01-14T14:20:00.000Z"
                            }
                          ],
                          "total": 15
                        }
                      }
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get All Subjects",
          "tags": [
            "Subjects"
          ]
        },
        "post": {
          "description": "\n        Creates a new subject for a specific academic session.\n\n        **Required Information:**\n        - **Session ID**: Must be a valid academic session ID (provided in URL path)\n        - **Name**: Subject name (max 100 characters, must be unique within session)\n        - **Type**: Either \"THEORY\" or \"PRACTICAL\"\n        - **Marks**: Maximum marks for the subject (1-1000)\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Permissions:** Only Institute Owners can create subjects.\n\n        **Validation Rules:**\n        - Subject name must be unique within the academic session and type combination\n        - Marks must be between 1 and 1000\n        - Type must be either THEORY or PRACTICAL\n        - Academic session must exist and be accessible\n\n        **Use Cases:**\n        - Setting up curriculum for a new academic session\n        - Adding new subjects to existing academic sessions\n        - Configuring subject-specific settings like marks allocation\n\n        **Business Logic:**\n        - Subjects are automatically set as active upon creation\n        - Each subject is tied to a specific academic session\n        - Subject name + session + type combination must be unique\n      ",
          "operationId": "create",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the academic session",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "description": "Subject data including name, type, and marks",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateSubjectDto"
                },
                "examples": {
                  "mathematics-theory": {
                    "summary": "Mathematics Theory Subject",
                    "description": "Example for creating a theoretical mathematics subject",
                    "value": {
                      "name": "Mathematics",
                      "type": "THEORY",
                      "marks": 100
                    }
                  },
                  "physics-practical": {
                    "summary": "Physics Practical Subject",
                    "description": "Example for creating a practical physics subject",
                    "value": {
                      "name": "Physics Laboratory",
                      "type": "PRACTICAL",
                      "marks": 50
                    }
                  },
                  "computer-science": {
                    "summary": "Computer Science Subject",
                    "description": "Example for creating a computer science subject",
                    "value": {
                      "name": "Computer Science",
                      "type": "THEORY",
                      "marks": 75
                    }
                  }
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Subject created successfully",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/SubjectResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Subject with same name and type already exists in this session",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Subject",
          "tags": [
            "Subjects"
          ]
        }
      },
      "/v1/sms/sessions/{sessionId}/subjects/{subjectId}": {
        "patch": {
          "description": "\n        Updates an existing subject with new information. All fields are optional.\n\n        **Required Information:**\n        - **Session ID**: Must be a valid academic session ID (provided in URL path)\n        - **Subject ID**: Must be a valid subject ID (provided in URL path)\n\n        **Optional Fields:**\n        - **Name**: New subject name (max 100 characters)\n        - **Type**: New subject type (\"THEORY\" or \"PRACTICAL\")\n        - **Marks**: New maximum marks (1-1000)\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Permissions:** Only Institute Owners can update subjects.\n\n        **Validation Rules:**\n        - Subject must exist and be accessible\n        - If changing name/type, the new combination must be unique within the session\n        - Marks must be between 1 and 1000 if provided\n        - Academic session must exist if being changed\n\n        **Use Cases:**\n        - Correcting subject information\n        - Adjusting marks allocation for subjects\n        - Changing subject type from theory to practical or vice versa\n        - Updating subject names for clarity\n\n        **Business Logic:**\n        - Only provided fields will be updated (partial update)\n        - Subject ID and creation timestamp cannot be changed\n        - Academic session can be changed if needed\n      ",
          "operationId": "update",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the academic session",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            },
            {
              "name": "subjectId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the subject to update",
              "schema": {
                "example": "456e7890-e89b-12d3-a456-426614174001",
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "description": "Subject update data (all fields optional)",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateSubjectDto"
                },
                "examples": {
                  "update-marks": {
                    "summary": "Update Subject Marks",
                    "description": "Example for updating only the marks of a subject",
                    "value": {
                      "marks": 80
                    }
                  },
                  "update-name-and-type": {
                    "summary": "Update Name and Type",
                    "description": "Example for updating subject name and type",
                    "value": {
                      "name": "Advanced Mathematics",
                      "type": "THEORY"
                    }
                  },
                  "complete-update": {
                    "summary": "Complete Update",
                    "description": "Example for updating all subject fields",
                    "value": {
                      "name": "Physics Laboratory Advanced",
                      "type": "PRACTICAL",
                      "marks": 60
                    }
                  }
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Subject updated successfully",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/SubjectResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Subject with same name and type already exists in this session",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Subject",
          "tags": [
            "Subjects"
          ]
        },
        "get": {
          "description": "\n        Retrieves detailed information about a specific subject by its ID.\n\n        **Required Information:**\n        - **Session ID**: Must be a valid academic session ID (provided in URL path)\n        - **Subject ID**: Must be a valid subject ID (provided in URL path)\n\n        **Authentication Required:** Bearer token must be provided in the Authorization header.\n\n        **Permissions:** Only Institute Owners can view subject details.\n\n        **Use Cases:**\n        - Viewing detailed information about a specific subject\n        - Verifying subject configuration before making changes\n        - Displaying subject details in administrative interfaces\n        - Checking subject information for reporting purposes\n\n        **Response Details:**\n        - Returns complete subject information including all fields\n        - Includes metadata like creation timestamp and active status\n        - Shows associated academic session ID for reference\n\n        **Error Handling:**\n        - Returns 404 if subject doesn't exist\n        - Returns 403 if user doesn't have permission to view the subject\n        - Returns 400 if the provided IDs are invalid\n      ",
          "operationId": "getById",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the academic session",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            },
            {
              "name": "subjectId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the subject to retrieve",
              "schema": {
                "example": "456e7890-e89b-12d3-a456-426614174001",
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Successfully retrieved subject details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/SubjectResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get Subject by ID",
          "tags": [
            "Subjects"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/class-section-subjects": {
        "post": {
          "description": "Assigns a subject to a specific class section.",
          "operationId": "assignSubject",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AssignSubjectToSectionDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Successfully assigned subject to section",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/SectionSubjectAssignmentResponseDto"
                  }
                }
              }
            },
            "201": {
              "description": ""
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Assign Subject to Section",
          "tags": [
            "Section Subjects"
          ]
        },
        "get": {
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Section Subjects"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/class-section-subjects/{sectionSubjectId}": {
        "patch": {
          "operationId": "updateSubjectAssignment",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateSectionSubjectDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Section Subjects"
          ]
        },
        "get": {
          "operationId": "getSubjectAssignment",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Section Subjects"
          ]
        }
      },
      "/v1/sms/sections/{sectionId}/diaries": {
        "post": {
          "description": "Creates a new diary entry for a specific class section",
          "operationId": "create",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateDiaryDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the  newly created diary",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/DiaryResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Diary Entry",
          "tags": [
            "Diary"
          ]
        },
        "get": {
          "description": "Retrieves all diary entries for a specific class section",
          "operationId": "getAllBySectionId",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "description": "Unique identifier of the class section",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            },
            {
              "name": "subjectId",
              "required": false,
              "in": "path",
              "description": "Unique identifier of the subject",
              "schema": {
                "example": "123e4567-e89b-12d3-a456-426614174000",
                "type": "string"
              }
            },
            {
              "name": "date",
              "required": false,
              "in": "query",
              "description": "Filter by specific date (YYYY-MM-DD)",
              "schema": {
                "example": "2024-01-15",
                "type": "string"
              }
            },
            {
              "name": "month",
              "required": false,
              "in": "query",
              "description": "Filter by specific month (YYYY-MM)",
              "schema": {
                "example": "2024-01",
                "type": "string"
              }
            },
            {
              "name": "from",
              "required": false,
              "in": "query",
              "description": "Filter from date (YYYY-MM-DD)",
              "schema": {
                "example": "2024-01-15",
                "type": "string"
              }
            },
            {
              "name": "to",
              "required": false,
              "in": "query",
              "description": "Filter to date (YYYY-MM-DD)",
              "schema": {
                "example": "2024-01-31",
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Successfully retrieved diary entries",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ListDiaryResponseDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Get All Diary Entries",
          "tags": [
            "Diary"
          ]
        }
      },
      "/v1/sms/diaries/{diaryId}": {
        "patch": {
          "description": "Updates an existing diary entry",
          "operationId": "update",
          "parameters": [
            {
              "name": "diaryId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateDiaryDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated diary entry",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/DiaryResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Diary Entry",
          "tags": [
            "Diary"
          ]
        }
      },
      "/v1/sms/class-sections/{sectionId}/student-attendances": {
        "post": {
          "description": "Creates a new student attendance record",
          "operationId": "create",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateMultipleStudentAttendanceDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created student attendance record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 201
                      },
                      "message": {
                        "type": "string",
                        "example": "Student attendance created successfully"
                      },
                      "data": {
                        "type": "object",
                        "example": null,
                        "nullable": true
                      }
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        },
        "patch": {
          "description": "Updates a student attendance record",
          "operationId": "bulkUpdate",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateBulkStudentAttendanceDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated student attendance record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/StudentAttendanceResponseDto"
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        },
        "get": {
          "description": "Lists all student attendance records",
          "operationId": "findAll",
          "parameters": [
            {
              "name": "sectionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of student attendance records",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/StudentAttendanceResponseDto"
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "List Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        }
      },
      "/v1/sms/student-attendances": {
        "post": {
          "description": "Creates a new student attendance record. Only for branch admins.",
          "operationId": "createSingle",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateSingleStudentAttendanceDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created student attendance record",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/StudentAttendanceResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        }
      },
      "/v1/sms/student-attendances/{id}": {
        "patch": {
          "description": "Updates a student attendance record",
          "operationId": "updateSingle",
          "parameters": [
            {
              "name": "id",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateSingleStudentAttendanceDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated student attendance record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/StudentAttendanceResponseDto"
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        }
      },
      "/v1/sms/student-attendances/check-out": {
        "post": {
          "description": "Updates a student attendance record",
          "operationId": "checkout",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CheckoutStudentAttendanceDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated student attendance record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/StudentAttendanceResponseDto"
                    }
                  }
                }
              }
            },
            "201": {
              "description": ""
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Student Attendance",
          "tags": [
            "Student Attendances"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/exams": {
        "post": {
          "description": "Creates a new exam for a specific academic session",
          "operationId": "create",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateExamDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created exam with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Exam",
          "tags": [
            "Exams"
          ]
        },
        "get": {
          "description": "Lists all exams for a specific academic session",
          "operationId": "findAll",
          "parameters": [
            {
              "name": "sessionId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of exams",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ListExamsDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "List Exams",
          "tags": [
            "Exams"
          ]
        }
      },
      "/v1/sms/academic-sessions/{sessionId}/exams/{examId}": {
        "patch": {
          "description": "Updates an existing exam",
          "operationId": "update",
          "parameters": [
            {
              "name": "examId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateExamDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated exam with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Exam",
          "tags": [
            "Exams"
          ]
        }
      },
      "/v1/status": {
        "get": {
          "operationId": "getAppStatus",
          "parameters": [],
          "responses": {
            "200": {
              "description": "Returns pong message with timestamp on successful request",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PingResponseDto"
                  }
                }
              }
            }
          },
          "tags": [
            "Health Check"
          ]
        }
      },
      "/v1/sms/branches/{branchId}/student-card-templates": {
        "post": {
          "operationId": "create",
          "parameters": [
            {
              "name": "branchId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateCardTemplateDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "Student Card Templates"
          ]
        },
        "get": {
          "operationId": "get",
          "parameters": [
            {
              "name": "branchId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Student Card Templates"
          ]
        }
      },
      "/v1/sms/branches/{branchId}/student-card-templates/{templateId}": {
        "get": {
          "operationId": "getById",
          "parameters": [
            {
              "name": "branchId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "templateId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Student Card Templates"
          ]
        },
        "patch": {
          "operationId": "update",
          "parameters": [
            {
              "name": "branchId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "templateId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateCardTemplateDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Student Card Templates"
          ]
        }
      },
      "/v1/sms/academic-sessions/{academicSessionId}/enrollments": {
        "get": {
          "operationId": "getAll",
          "parameters": [],
          "responses": {
            "200": {
              "description": ""
            }
          },
          "tags": [
            "Enrollments"
          ]
        },
        "post": {
          "operationId": "create",
          "parameters": [],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateEnrollmentDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": ""
            }
          },
          "tags": [
            "Enrollments"
          ]
        }
      },
      "/v1/sms/exams/{examId}/class-sections": {
        "post": {
          "description": "Assigns an exam to multiple class sections, creating exam schedules for each section",
          "operationId": "assignExamToClassSection",
          "parameters": [
            {
              "name": "examId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AssignClassSectionExamDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created class section exam assignments",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/ClassSectionExamResponseDto"
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Assign Exam to Class Sections",
          "tags": [
            "Assign Exams to Class Sections"
          ]
        },
        "get": {
          "description": "Lists all class sections assigned to a specific exam with their details",
          "operationId": "getAllClassSectionsForExam",
          "parameters": [
            {
              "name": "examId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of class section exam assignments",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ListClassSectionExamsResponseDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "List Class Section Exams",
          "tags": [
            "Assign Exams to Class Sections"
          ]
        },
        "patch": {
          "description": "Updates the class sections assigned to an exam by replacing existing assignments",
          "operationId": "updateClassSectionExamAssignments",
          "parameters": [
            {
              "name": "examId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateClassSectionExamDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated class section exam assignments",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "items": {
                      "$ref": "#/components/schemas/ClassSectionExamResponseDto"
                    }
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Class Section Exam Assignments",
          "tags": [
            "Assign Exams to Class Sections"
          ]
        }
      },
      "/v1/sms/class-section-exams/{classSectionExamId}/schedules": {
        "post": {
          "description": "Creates a new exam schedule for a specific class section exam and subject",
          "operationId": "create",
          "parameters": [
            {
              "name": "classSectionExamId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateExamScheduleDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created exam schedule with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamScheduleResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Exam Schedule",
          "tags": [
            "Exam Schedules"
          ]
        },
        "get": {
          "description": "Lists all exam schedules for a specific class section exam with exam, class section, and subject details",
          "operationId": "findAll",
          "parameters": [
            {
              "name": "classSectionExamId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of exam schedules",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ListExamSchedulesResponseDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "List Exam Schedules",
          "tags": [
            "Exam Schedules"
          ]
        }
      },
      "/v1/sms/class-section-exams/{classSectionExamId}/schedules/{scheduleId}": {
        "patch": {
          "description": "Updates an existing exam schedule with new date, time, or marks information",
          "operationId": "update",
          "parameters": [
            {
              "name": "scheduleId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateExamScheduleDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated exam schedule with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamScheduleResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Exam Schedule",
          "tags": [
            "Exam Schedules"
          ]
        }
      },
      "/v1/sms/exam-schedules/{examScheduleId}/results": {
        "post": {
          "description": "Creates a new exam result for a student in a specific exam schedule",
          "operationId": "create",
          "parameters": [
            {
              "name": "examScheduleId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateExamResultDto"
                }
              }
            }
          },
          "responses": {
            "201": {
              "description": "Returns the newly created exam result with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamResultResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Create Exam Result",
          "tags": [
            "Exam Results"
          ]
        },
        "get": {
          "description": "Lists all exam results for a specific exam schedule with student and class section details",
          "operationId": "findAll",
          "parameters": [
            {
              "name": "examScheduleId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "limit",
              "required": false,
              "in": "query",
              "description": "Maximum number of items to retrieve",
              "schema": {
                "example": 10,
                "type": "number"
              }
            },
            {
              "name": "offset",
              "required": false,
              "in": "query",
              "description": "Number of items to skip",
              "schema": {
                "example": 1,
                "type": "number"
              }
            }
          ],
          "responses": {
            "200": {
              "description": "Returns the list of exam results",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ListExamResultsResponseDto"
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "List Exam Results",
          "tags": [
            "Exam Results"
          ]
        }
      },
      "/v1/sms/exam-schedules/{examScheduleId}/results/{resultId}": {
        "patch": {
          "description": "Updates an existing exam result with new marks, remarks, or attendance status",
          "operationId": "update",
          "parameters": [
            {
              "name": "resultId",
              "required": true,
              "in": "path",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UpdateExamResultDto"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Returns the updated exam result with complete details",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExamResultResponseDto"
                  }
                }
              }
            },
            "400": {
              "description": "Bad Request - Invalid data provided",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 400
                      },
                      "message": {
                        "type": "string",
                        "example": "Validation failed"
                      },
                      "data": {
                        "type": "object",
                        "properties": {
                          "field": {
                            "type": "string"
                          },
                          "message": {
                            "type": "string"
                          }
                        },
                        "example": {
                          "email": "Invalid email",
                          "password": "Password is required"
                        }
                      }
                    }
                  }
                }
              }
            },
            "403": {
              "description": "Forbidden - User does not have permission to access resource",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 403
                      },
                      "message": {
                        "type": "string",
                        "example": "You do not have the required permissions to access this resource"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "404": {
              "description": "Not Found - Record with given input does not exist",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 404
                      },
                      "message": {
                        "type": "string",
                        "example": "Record not found"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "409": {
              "description": "Conflict - Updated details conflict with existing record",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 409
                      },
                      "message": {
                        "type": "string",
                        "example": "Record with these details already exists"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            },
            "500": {
              "description": "Internal Server Error",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "statusCode": {
                        "type": "number",
                        "example": 500
                      },
                      "message": {
                        "type": "string",
                        "example": "Internal server error"
                      },
                      "data": {
                        "type": "object",
                        "nullable": true,
                        "example": null
                      }
                    }
                  }
                }
              }
            }
          },
          "summary": "Update Exam Result",
          "tags": [
            "Exam Results"
          ]
        }
      }
    },
    "info": {
      "title": "Ebridge API",
      "description": "\n      Complete API documentation for the Ebridge platform.\n\n      This API provides endpoints for managing packages, users, and other core business entities.\n      All protected routes require authentication via Bearer token obtained from the auth endpoints.\n\n      ## Environment Information\n      - **Current Version**: 1.0.0\n      - **Base URL**: https://api.ebridge.com\n      - **Environment**: development\n    ",
      "version": "1.0.0",
      "contact": {
        "name": "Ebridge API Support",
        "url": "https://ebridge.com/support",
        "email": "<EMAIL>"
      },
      "license": {
        "name": "Proprietary",
        "url": "https://ebridge.com/terms"
      }
    },
    "tags": [
      {
        "name": "Authentication",
        "description": "Endpoints for user authentication and token management"
      },
      {
        "name": "Subscription Plans",
        "description": "Endpoints for managing subscription plans"
      },
      {
        "name": "Subscriptions",
        "description": "Endpoints for managing subscriptions"
      },
      {
        "name": "Institutes",
        "description": "Endpoints for managing institutes"
      },
      {
        "name": "Institute Owners",
        "description": "Endpoints for managing institute owners"
      },
      {
        "name": "Institute Branches",
        "description": "Endpoints for managing institute branches"
      },
      {
        "name": "Academic Sessions",
        "description": "Endpoints for managing academic sessions"
      },
      {
        "name": "Staff",
        "description": "Endpoints for managing staff"
      },
      {
        "name": "Classes",
        "description": "Endpoints for managing classes"
      },
      {
        "name": "Subjects",
        "description": "Endpoints for managing academic subjects"
      },
      {
        "name": "Students",
        "description": "Endpoints for managing students"
      },
      {
        "name": "Guardians",
        "description": "Endpoints for managing guardians"
      },
      {
        "name": "Diary",
        "description": "Endpoints for managing class diary entries"
      },
      {
        "name": "Exams",
        "description": "Endpoints for managing exams"
      },
      {
        "name": "Exam Schedules",
        "description": "Endpoints for managing exam schedules"
      },
      {
        "name": "Assign Exams to Class Sections",
        "description": "Endpoints for assigning exams to class sections"
      },
      {
        "name": "Exam Results",
        "description": "Endpoints for managing exam results"
      },
      {
        "name": "Student Attendances",
        "description": "Endpoints for managing student attendance"
      },
      {
        "name": "Student Card Templates",
        "description": "Endpoints for managing student card templates"
      },
      {
        "name": "Enrollments",
        "description": "Endpoints for managing student enrollments"
      },
      {
        "name": "Health Check",
        "description": "Endpoints for checking the API's health and status"
      }
    ],
    "servers": [
      {
        "url": "https://api.ebridge.com",
        "description": "Production Server"
      },
      {
        "url": "https://staging-api.ebridge.com",
        "description": "Staging Server"
      },
      {
        "url": "http://*************:3000",
        "description": "Local Development"
      }
    ],
    "components": {
      "securitySchemes": {
        "access-token": {
          "scheme": "bearer",
          "bearerFormat": "JWT",
          "type": "http",
          "name": "Authorization",
          "description": "Enter JWT token",
          "in": "header"
        }
      },
      "schemas": {
        "BaseUserProfileResponseDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "id": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "createdAt": {},
            "role": {
              "type": "number"
            },
            "isActive": {
              "type": "boolean"
            },
            "isPasswordTemporary": {
              "type": "boolean"
            }
          },
          "required": [
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "cnic",
            "id",
            "createdAt",
            "role",
            "isActive",
            "isPasswordTemporary"
          ]
        },
        "InstituteOwnerProfileResponseDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "id": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "createdAt": {},
            "role": {
              "type": "number"
            },
            "isActive": {
              "type": "boolean"
            },
            "isPasswordTemporary": {
              "type": "boolean"
            }
          },
          "required": [
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "cnic",
            "id",
            "createdAt",
            "role",
            "isActive",
            "isPasswordTemporary"
          ]
        },
        "TeacherProfileResponseDto": {
          "type": "object",
          "properties": {
            "id": {
              "type": "string"
            },
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "email": {
              "type": "string",
              "format": "email",
              "maxLength": 100
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "designation": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "department": {
              "type": "string",
              "enum": [
                "ACADEMIC",
                "ADMINISTRATION",
                "SUPPORT"
              ]
            },
            "type": {
              "type": "string",
              "enum": [
                "TEACHER",
                "BRANCH_ADMIN",
                "ACCOUNTANT",
                "SUPPORT_STAFF"
              ]
            },
            "salary": {
              "type": "number",
              "minimum": -****************,
              "exclusiveMinimum": false,
              "maximum": ****************,
              "exclusiveMaximum": false
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "createdAt": {},
            "branchId": {
              "type": "string"
            },
            "classTeacherOf": {
              "description": "List of class sections where this teacher is the class teacher",
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "minLength": 2,
                    "maxLength": 70,
                    "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                  },
                  "isActive": {
                    "type": "boolean",
                    "default": true
                  },
                  "id": {
                    "type": "string"
                  },
                  "totalStudents": {
                    "type": "number"
                  },
                  "createdAt": {},
                  "class": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string"
                      },
                      "name": {
                        "type": "string"
                      }
                    },
                    "required": [
                      "id",
                      "name"
                    ]
                  }
                },
                "required": [
                  "name",
                  "id",
                  "totalStudents",
                  "createdAt",
                  "class"
                ]
              }
            },
            "subjectTeacherOf": {
              "description": "List of subject teaching assignments for this teacher",
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "id": {
                    "type": "string"
                  },
                  "academicSessionId": {
                    "type": "string",
                    "minLength": 36,
                    "maxLength": 36
                  },
                  "class": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "name": {
                        "type": "string"
                      }
                    },
                    "required": [
                      "id",
                      "name"
                    ]
                  },
                  "section": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "name": {
                        "type": "string"
                      }
                    },
                    "required": [
                      "id",
                      "name"
                    ]
                  },
                  "subject": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "name": {
                        "type": "string"
                      },
                      "totalStudents": {
                        "type": "number"
                      }
                    },
                    "required": [
                      "id",
                      "name",
                      "totalStudents"
                    ]
                  },
                  "createdAt": {}
                },
                "required": [
                  "id",
                  "academicSessionId",
                  "class",
                  "section",
                  "subject",
                  "createdAt"
                ]
              }
            }
          },
          "required": [
            "id",
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "designation",
            "department",
            "type",
            "salary",
            "cnic",
            "createdAt",
            "branchId",
            "classTeacherOf",
            "subjectTeacherOf"
          ]
        },
        "PackageDto": {
          "type": "object",
          "properties": {
            "id": {
              "type": "string"
            },
            "title": {
              "type": "string"
            },
            "description": {
              "type": "string",
              "nullable": true
            },
            "price": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": false,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "setupCharges": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "features": {
              "type": "array",
              "items": {
                "type": "string"
              }
            },
            "branches": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 10,
              "exclusiveMaximum": false
            },
            "students": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 10000,
              "exclusiveMaximum": false
            },
            "createdAt": {}
          },
          "required": [
            "id",
            "title",
            "description",
            "price",
            "setupCharges",
            "features",
            "branches",
            "students",
            "createdAt"
          ]
        },
        "CreateSubscriptionPlanDto": {
          "type": "object",
          "properties": {
            "title": {
              "type": "string"
            },
            "description": {
              "type": "string"
            },
            "price": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": false,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "setupCharges": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "features": {
              "type": "array",
              "items": {
                "type": "string"
              }
            },
            "branches": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 10,
              "exclusiveMaximum": false
            },
            "students": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 10000,
              "exclusiveMaximum": false
            }
          },
          "required": [
            "title",
            "price",
            "setupCharges",
            "features",
            "branches",
            "students"
          ]
        },
        "SignInDto": {
          "type": "object",
          "properties": {
            "email": {
              "type": "string",
              "format": "email"
            },
            "password": {
              "type": "string"
            }
          },
          "required": [
            "email",
            "password"
          ]
        },
        "SmsLoginResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "accessToken": {
                  "description": "JWT access token for authenticating API requests",
                  "type": "string"
                },
                "role": {
                  "description": "User role code (e.g., 3000)",
                  "type": "number"
                },
                "refreshToken": {
                  "description": "JWT refresh token (only provided if user doesn't have temporary password)",
                  "type": "string"
                }
              },
              "required": [
                "accessToken",
                "role"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "PlatformLoginResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "accessToken": {
                  "description": "JWT access token for authenticating API requests",
                  "type": "string"
                },
                "role": {
                  "description": "User role code (e.g., 3000)",
                  "type": "number"
                }
              },
              "required": [
                "accessToken",
                "role"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "SetPasswordDto": {
          "type": "object",
          "properties": {
            "password": {
              "type": "string",
              "minLength": 8,
              "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+"
            }
          },
          "required": [
            "password"
          ]
        },
        "RefreshTokenResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "accessToken": {
                  "description": "New JWT access token for authenticating API requests",
                  "type": "string"
                },
                "role": {
                  "description": "User role code (e.g., 3000)",
                  "type": "number"
                }
              },
              "required": [
                "accessToken",
                "role"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "CreateOwnerDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250
            },
            "password": {
              "type": "string",
              "minLength": 8,
              "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+"
            }
          },
          "required": [
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "cnic",
            "password"
          ]
        },
        "OwnerResponseDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "id": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "createdAt": {},
            "role": {
              "type": "number"
            },
            "isActive": {
              "type": "boolean"
            },
            "isPasswordTemporary": {
              "type": "boolean"
            }
          },
          "required": [
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "cnic",
            "id",
            "createdAt",
            "role",
            "isActive",
            "isPasswordTemporary"
          ]
        },
        "InstituteResponseDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "isBasicSetupComplete": {
              "type": "boolean",
              "default": false
            },
            "logo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "id": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "createdAt": {},
            "ownerId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          },
          "required": [
            "name",
            "email",
            "logo",
            "id",
            "createdAt",
            "ownerId"
          ]
        },
        "UpdateInstituteDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "isBasicSetupComplete": {
              "type": "boolean",
              "default": false
            },
            "logo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            }
          }
        },
        "createInstituteDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "isBasicSetupComplete": {
              "type": "boolean",
              "default": false
            },
            "logo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            }
          },
          "required": [
            "name",
            "email",
            "logo"
          ]
        },
        "BranchResponseDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string"
            },
            "address": {
              "type": "string"
            },
            "phone": {
              "type": "string"
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "isMain": {
              "type": "boolean",
              "default": false
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "id": {
              "type": "string"
            },
            "instituteId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "createdAt": {}
          },
          "required": [
            "name",
            "address",
            "phone",
            "email",
            "id",
            "instituteId",
            "createdAt"
          ]
        },
        "CreateBranchDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string"
            },
            "address": {
              "type": "string"
            },
            "phone": {
              "type": "string"
            },
            "email": {
              "type": "string",
              "format": "email"
            },
            "isMain": {
              "type": "boolean",
              "default": false
            },
            "isActive": {
              "type": "boolean",
              "default": true
            }
          },
          "required": [
            "name",
            "address",
            "phone",
            "email"
          ]
        },
        "CreateAcademicSessionDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "startDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "endDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "isActive": {
              "type": "boolean",
              "default": true
            }
          },
          "required": [
            "name",
            "startDate",
            "endDate"
          ]
        },
        "CreateStaffDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "email": {
              "type": "string",
              "format": "email",
              "maxLength": 100
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "password": {
              "type": "string",
              "minLength": 8,
              "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+",
              "nullable": true
            },
            "designation": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "department": {
              "type": "string",
              "enum": [
                "ACADEMIC",
                "ADMINISTRATION",
                "SUPPORT"
              ]
            },
            "type": {
              "type": "string",
              "enum": [
                "TEACHER",
                "BRANCH_ADMIN",
                "ACCOUNTANT",
                "SUPPORT_STAFF"
              ]
            },
            "salary": {
              "type": "number",
              "minimum": -****************,
              "exclusiveMinimum": false,
              "maximum": ****************,
              "exclusiveMaximum": false
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            }
          },
          "required": [
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "designation",
            "department",
            "type",
            "salary",
            "cnic"
          ]
        },
        "StaffProfileResponseDto": {
          "type": "object",
          "properties": {
            "id": {
              "type": "string"
            },
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "email": {
              "type": "string",
              "format": "email",
              "maxLength": 100
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "designation": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "department": {
              "type": "string",
              "enum": [
                "ACADEMIC",
                "ADMINISTRATION",
                "SUPPORT"
              ]
            },
            "type": {
              "type": "string",
              "enum": [
                "TEACHER",
                "BRANCH_ADMIN",
                "ACCOUNTANT",
                "SUPPORT_STAFF"
              ]
            },
            "salary": {
              "type": "number",
              "minimum": -****************,
              "exclusiveMinimum": false,
              "maximum": ****************,
              "exclusiveMaximum": false
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "createdAt": {},
            "branchId": {
              "type": "string"
            }
          },
          "required": [
            "id",
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "designation",
            "department",
            "type",
            "salary",
            "cnic",
            "createdAt",
            "branchId"
          ]
        },
        "UpdateStaffDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "phone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250,
              "nullable": true
            },
            "password": {
              "type": "string",
              "minLength": 8,
              "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+",
              "nullable": true
            },
            "designation": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "salary": {
              "type": "number",
              "minimum": -****************,
              "exclusiveMinimum": false,
              "maximum": ****************,
              "exclusiveMaximum": false
            },
            "cnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            }
          }
        },
        "BasicTeacherInfoResponseDto": {
          "type": "object",
          "properties": {
            "id": {
              "description": "Teacher's unique identifier",
              "type": "string"
            },
            "name": {
              "description": "Teacher's full name",
              "type": "string"
            }
          },
          "required": [
            "id",
            "name"
          ]
        },
        "CreateClassDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "maximumStudents": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            },
            "feePerMonth": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "sections": {
              "type": "array",
              "minItems": 1,
              "items": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "minLength": 2,
                    "maxLength": 70,
                    "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                  },
                  "isActive": {
                    "type": "boolean",
                    "default": true
                  },
                  "classTeacherId": {
                    "type": "string",
                    "minLength": 36,
                    "maxLength": 36
                  }
                },
                "required": [
                  "name",
                  "classTeacherId"
                ]
              }
            }
          },
          "required": [
            "name",
            "maximumStudents",
            "feePerMonth",
            "sections"
          ]
        },
        "UpdateClassDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "maximumStudents": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            },
            "feePerMonth": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "sections": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "minLength": 2,
                    "maxLength": 70,
                    "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                  },
                  "isActive": {
                    "type": "boolean",
                    "default": true
                  },
                  "classTeacherId": {
                    "type": "string",
                    "minLength": 36,
                    "maxLength": 36
                  },
                  "id": {
                    "type": "string",
                    "minLength": 36,
                    "maxLength": 36
                  }
                },
                "required": [
                  "name",
                  "classTeacherId",
                  "id"
                ]
              }
            }
          }
        },
        "CreateStudentDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "fatherName": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "religion": {
              "type": "string",
              "enum": [
                "ISLAM",
                "CHRISTIANITY",
                "HINDUISM",
                "BUDDHISM",
                "SIKHISM",
                "JUDAISM",
                "OTHER"
              ]
            },
            "registrationNumber": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "email": {
              "type": "string",
              "format": "email",
              "maxLength": 100
            },
            "address": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "gender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "photo": {
              "type": "string",
              "minLength": 5,
              "maxLength": 250
            },
            "monthlyFee": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000000,
              "exclusiveMaximum": false
            },
            "admissionDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "dateOfBirth": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "previousSchool": {
              "type": "string",
              "maxLength": 100
            },
            "guardianName": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "guardianPhone": {
              "type": "string",
              "minLength": 10,
              "maxLength": 20,
              "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
            },
            "guardianEmail": {
              "type": "string",
              "format": "email",
              "maxLength": 100
            },
            "guardianPassword": {
              "type": "string",
              "minLength": 8,
              "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+"
            },
            "guardianAddress": {
              "type": "string",
              "minLength": 1,
              "maxLength": 255
            },
            "guardianGender": {
              "type": "string",
              "enum": [
                "MALE",
                "FEMALE",
                "OTHER"
              ]
            },
            "guardianRelation": {
              "type": "string",
              "enum": [
                "FATHER",
                "MOTHER",
                "GUARDIAN"
              ]
            },
            "guardianCnic": {
              "type": "string",
              "minLength": 1,
              "maxLength": 15
            },
            "classSectionId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          },
          "required": [
            "name",
            "fatherName",
            "religion",
            "address",
            "gender",
            "monthlyFee",
            "admissionDate",
            "dateOfBirth",
            "guardianName",
            "guardianPhone",
            "guardianEmail",
            "guardianPassword",
            "guardianAddress",
            "guardianGender",
            "guardianRelation",
            "guardianCnic",
            "classSectionId"
          ]
        },
        "StudentResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "name": {
                        "type": "string",
                        "minLength": 2,
                        "maxLength": 70,
                        "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                      },
                      "fatherName": {
                        "type": "string",
                        "minLength": 2,
                        "maxLength": 70,
                        "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                      },
                      "religion": {
                        "type": "string",
                        "enum": [
                          "ISLAM",
                          "CHRISTIANITY",
                          "HINDUISM",
                          "BUDDHISM",
                          "SIKHISM",
                          "JUDAISM",
                          "OTHER"
                        ]
                      },
                      "registrationNumber": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 100
                      },
                      "email": {
                        "type": "string",
                        "format": "email",
                        "maxLength": 100
                      },
                      "address": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 255
                      },
                      "gender": {
                        "type": "string",
                        "enum": [
                          "MALE",
                          "FEMALE",
                          "OTHER"
                        ]
                      },
                      "photo": {
                        "type": "string",
                        "minLength": 5,
                        "maxLength": 250
                      },
                      "monthlyFee": {
                        "type": "number",
                        "minimum": 0,
                        "exclusiveMinimum": true,
                        "maximum": 1000000,
                        "exclusiveMaximum": false
                      },
                      "admissionDate": {
                        "type": "string",
                        "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                      },
                      "dateOfBirth": {
                        "type": "string",
                        "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                      },
                      "previousSchool": {
                        "type": "string",
                        "maxLength": 100
                      },
                      "guardianName": {
                        "type": "string",
                        "minLength": 2,
                        "maxLength": 70,
                        "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                      },
                      "guardianPhone": {
                        "type": "string",
                        "minLength": 10,
                        "maxLength": 20,
                        "pattern": "^(\\+?\\d{1,3})?[ -]?\\(?\\d{2,4}\\)?[ -]?\\d{3,4}[ -]?\\d{3,4}$"
                      },
                      "guardianEmail": {
                        "type": "string",
                        "format": "email",
                        "maxLength": 100
                      },
                      "guardianPassword": {
                        "type": "string",
                        "minLength": 8,
                        "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&#+-_=^~\\\\[\\]{}|:;<>,.()]).+"
                      },
                      "guardianAddress": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 255
                      },
                      "guardianGender": {
                        "type": "string",
                        "enum": [
                          "MALE",
                          "FEMALE",
                          "OTHER"
                        ]
                      },
                      "guardianRelation": {
                        "type": "string",
                        "enum": [
                          "FATHER",
                          "MOTHER",
                          "GUARDIAN"
                        ]
                      },
                      "guardianCnic": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 15
                      },
                      "classSectionId": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "rollNumber": {
                        "type": "number"
                      },
                      "createdAt": {},
                      "enrollmentId": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      }
                    },
                    "required": [
                      "name",
                      "fatherName",
                      "religion",
                      "address",
                      "gender",
                      "monthlyFee",
                      "admissionDate",
                      "dateOfBirth",
                      "guardianName",
                      "guardianPhone",
                      "guardianEmail",
                      "guardianPassword",
                      "guardianAddress",
                      "guardianGender",
                      "guardianRelation",
                      "guardianCnic",
                      "classSectionId",
                      "id",
                      "rollNumber",
                      "createdAt",
                      "enrollmentId"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "CreateClassSectionDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 2,
              "maxLength": 70,
              "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
            },
            "isActive": {
              "type": "boolean",
              "default": true
            },
            "classTeacherId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          },
          "required": [
            "name",
            "classTeacherId"
          ]
        },
        "SubjectResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string",
                  "minLength": 1,
                  "maxLength": 100
                },
                "type": {
                  "type": "string",
                  "enum": [
                    "THEORY",
                    "PRACTICAL"
                  ]
                },
                "marks": {
                  "type": "number",
                  "minimum": 1,
                  "exclusiveMinimum": false,
                  "maximum": 1000,
                  "exclusiveMaximum": false
                },
                "id": {
                  "description": "Unique identifier for the subject",
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "isActive": {
                  "description": "Whether the subject is currently active",
                  "type": "boolean"
                },
                "academicSessionId": {
                  "description": "ID of the academic session this subject belongs to",
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "createdAt": {
                  "description": "Date and time when the subject was created"
                }
              },
              "required": [
                "name",
                "type",
                "marks",
                "id",
                "isActive",
                "academicSessionId",
                "createdAt"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "CreateSubjectDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "type": {
              "type": "string",
              "enum": [
                "THEORY",
                "PRACTICAL"
              ]
            },
            "marks": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 1000,
              "exclusiveMaximum": false
            }
          },
          "required": [
            "name",
            "type",
            "marks"
          ]
        },
        "UpdateSubjectDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "type": {
              "type": "string",
              "enum": [
                "THEORY",
                "PRACTICAL"
              ]
            },
            "marks": {
              "type": "number",
              "minimum": 1,
              "exclusiveMinimum": false,
              "maximum": 1000,
              "exclusiveMaximum": false
            }
          }
        },
        "AssignSubjectToSectionDto": {
          "type": "object",
          "properties": {
            "classSectionId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "subjectId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "subjectTeacherId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          },
          "required": [
            "classSectionId",
            "subjectId",
            "subjectTeacherId"
          ]
        },
        "SectionSubjectAssignmentResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "id": {
                  "type": "string"
                },
                "academicSessionId": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "class": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "id",
                    "name"
                  ]
                },
                "section": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "id",
                    "name"
                  ]
                },
                "subject": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "type": "string"
                    },
                    "totalStudents": {
                      "type": "number"
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "totalStudents"
                  ]
                },
                "subjectTeacher": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "id",
                    "name"
                  ]
                },
                "createdAt": {}
              },
              "required": [
                "id",
                "academicSessionId",
                "class",
                "section",
                "subject",
                "subjectTeacher",
                "createdAt"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateSectionSubjectDto": {
          "type": "object",
          "properties": {
            "classSectionId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "subjectId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "subjectTeacherId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          }
        },
        "ListDiaryResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "description": "Diary entry ID",
                        "type": "string"
                      },
                      "content": {
                        "description": "Diary entry content",
                        "type": "string"
                      },
                      "date": {
                        "description": "Date of the diary entry",
                        "type": "string"
                      },
                      "createdAt": {
                        "description": "Creation timestamp"
                      },
                      "subject": {
                        "description": "Subject information",
                        "type": "object",
                        "properties": {
                          "id": {
                            "description": "Subject ID",
                            "type": "string"
                          },
                          "name": {
                            "description": "Subject name",
                            "type": "string"
                          },
                          "type": {
                            "description": "Subject type",
                            "type": "string",
                            "enum": [
                              "THEORY",
                              "PRACTICAL"
                            ]
                          },
                          "marks": {
                            "description": "Subject marks",
                            "type": "number"
                          }
                        },
                        "required": [
                          "id",
                          "name",
                          "type",
                          "marks"
                        ]
                      },
                      "createdBy": {
                        "description": "Teacher information",
                        "type": "object",
                        "properties": {
                          "id": {
                            "description": "User ID",
                            "type": "string"
                          },
                          "name": {
                            "description": "User name",
                            "type": "string"
                          },
                          "photo": {
                            "description": "User photo",
                            "type": "string",
                            "nullable": true
                          }
                        },
                        "required": [
                          "id",
                          "name"
                        ]
                      },
                      "classSection": {
                        "description": "Class section information",
                        "type": "object",
                        "properties": {
                          "id": {
                            "description": "Class section ID",
                            "type": "string"
                          },
                          "name": {
                            "description": "Class section name",
                            "type": "string"
                          },
                          "totalStudents": {
                            "description": "Total students in the class section",
                            "type": "number"
                          },
                          "class": {
                            "description": "Class information",
                            "type": "object",
                            "properties": {
                              "id": {
                                "description": "Class ID",
                                "type": "string"
                              },
                              "name": {
                                "description": "Class name",
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          }
                        },
                        "required": [
                          "id",
                          "name",
                          "totalStudents",
                          "class"
                        ]
                      }
                    },
                    "required": [
                      "id",
                      "content",
                      "date",
                      "createdAt",
                      "subject",
                      "createdBy",
                      "classSection"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "CreateDiaryDto": {
          "type": "object",
          "properties": {
            "subjectId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "content": {
              "type": "string"
            },
            "date": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            }
          },
          "required": [
            "subjectId",
            "content",
            "date"
          ]
        },
        "DiaryResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "id": {
                  "description": "Diary entry ID",
                  "type": "string"
                },
                "content": {
                  "description": "Diary entry content",
                  "type": "string"
                },
                "date": {
                  "description": "Date of the diary entry",
                  "type": "string"
                },
                "createdAt": {
                  "description": "Creation timestamp"
                },
                "subject": {
                  "description": "Subject information",
                  "type": "object",
                  "properties": {
                    "id": {
                      "description": "Subject ID",
                      "type": "string"
                    },
                    "name": {
                      "description": "Subject name",
                      "type": "string"
                    },
                    "type": {
                      "description": "Subject type",
                      "type": "string",
                      "enum": [
                        "THEORY",
                        "PRACTICAL"
                      ]
                    },
                    "marks": {
                      "description": "Subject marks",
                      "type": "number"
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "type",
                    "marks"
                  ]
                },
                "createdBy": {
                  "description": "Teacher information",
                  "type": "object",
                  "properties": {
                    "id": {
                      "description": "User ID",
                      "type": "string"
                    },
                    "name": {
                      "description": "User name",
                      "type": "string"
                    },
                    "photo": {
                      "description": "User photo",
                      "type": "string",
                      "nullable": true
                    }
                  },
                  "required": [
                    "id",
                    "name"
                  ]
                },
                "classSection": {
                  "description": "Class section information",
                  "type": "object",
                  "properties": {
                    "id": {
                      "description": "Class section ID",
                      "type": "string"
                    },
                    "name": {
                      "description": "Class section name",
                      "type": "string"
                    },
                    "totalStudents": {
                      "description": "Total students in the class section",
                      "type": "number"
                    },
                    "class": {
                      "description": "Class information",
                      "type": "object",
                      "properties": {
                        "id": {
                          "description": "Class ID",
                          "type": "string"
                        },
                        "name": {
                          "description": "Class name",
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "totalStudents",
                    "class"
                  ]
                }
              },
              "required": [
                "id",
                "content",
                "date",
                "createdAt",
                "subject",
                "createdBy",
                "classSection"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateDiaryDto": {
          "type": "object",
          "properties": {
            "content": {
              "type": "string"
            }
          },
          "required": [
            "content"
          ]
        },
        "CreateMultipleStudentAttendanceDto": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "enrollmentId": {
                "type": "string",
                "minLength": 36,
                "maxLength": 36
              },
              "date": {
                "type": "string",
                "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
              },
              "status": {
                "type": "string",
                "enum": [
                  "PRESENT",
                  "ABSENT",
                  "LATE",
                  "LEAVE"
                ]
              },
              "remarks": {
                "type": "string",
                "nullable": true
              }
            },
            "required": [
              "enrollmentId",
              "status"
            ]
          }
        },
        "UpdateBulkStudentAttendanceDto": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "remarks": {
                "type": "string",
                "nullable": true
              },
              "status": {
                "type": "string",
                "enum": [
                  "PRESENT",
                  "ABSENT",
                  "LATE",
                  "LEAVE"
                ]
              },
              "id": {
                "type": "string",
                "minLength": 36,
                "maxLength": 36
              }
            },
            "required": [
              "status",
              "id"
            ]
          }
        },
        "StudentAttendanceResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "date": {
                  "type": "string",
                  "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                },
                "status": {
                  "type": "string",
                  "enum": [
                    "PRESENT",
                    "ABSENT",
                    "LATE",
                    "LEAVE"
                  ]
                },
                "remarks": {
                  "type": "string",
                  "nullable": true
                },
                "id": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "createdAt": {},
                "checkedInTime": {
                  "nullable": true
                },
                "checkedOutTime": {
                  "nullable": true
                },
                "student": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string"
                    },
                    "name": {
                      "type": "string"
                    },
                    "rollNumber": {
                      "type": "number"
                    },
                    "enrollmentId": {
                      "type": "string"
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "rollNumber",
                    "enrollmentId"
                  ]
                },
                "classSection": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string"
                    },
                    "name": {
                      "type": "string"
                    },
                    "totalStudents": {
                      "type": "number"
                    },
                    "class": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "totalStudents",
                    "class"
                  ]
                }
              },
              "required": [
                "status",
                "id",
                "createdAt",
                "student",
                "classSection"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "CreateSingleStudentAttendanceDto": {
          "type": "object",
          "properties": {
            "enrollmentId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "status": {
              "type": "string",
              "enum": [
                "PRESENT",
                "ABSENT",
                "LATE",
                "LEAVE"
              ]
            },
            "remarks": {
              "type": "string",
              "nullable": true
            }
          },
          "required": [
            "enrollmentId",
            "status"
          ]
        },
        "UpdateSingleStudentAttendanceDto": {
          "type": "object",
          "properties": {
            "remarks": {
              "type": "string",
              "nullable": true
            },
            "status": {
              "type": "string",
              "enum": [
                "PRESENT",
                "ABSENT",
                "LATE",
                "LEAVE"
              ]
            },
            "checkedOutTime": {
              "type": "string",
              "format": "date-time",
              "nullable": true
            },
            "checkedInTime": {
              "type": "string",
              "format": "date-time",
              "nullable": true
            }
          },
          "required": [
            "status"
          ]
        },
        "CheckoutStudentAttendanceDto": {
          "type": "object",
          "properties": {
            "enrollmentId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            }
          },
          "required": [
            "enrollmentId"
          ]
        },
        "CreateExamDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "startDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "endDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            }
          },
          "required": [
            "name",
            "startDate",
            "endDate"
          ]
        },
        "ExamDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string",
                  "maxLength": 100
                },
                "startDate": {
                  "type": "string",
                  "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                },
                "endDate": {
                  "type": "string",
                  "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                },
                "id": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "createdAt": {}
              },
              "required": [
                "name",
                "startDate",
                "endDate",
                "id",
                "createdAt"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateExamDto": {
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "maxLength": 100
            },
            "startDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "endDate": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            }
          }
        },
        "ListExamsDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "name": {
                        "type": "string",
                        "maxLength": 100
                      },
                      "startDate": {
                        "type": "string",
                        "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                      },
                      "endDate": {
                        "type": "string",
                        "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                      },
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "createdAt": {}
                    },
                    "required": [
                      "name",
                      "startDate",
                      "endDate",
                      "id",
                      "createdAt"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "PingResponseDto": {
          "type": "object",
          "properties": {
            "message": {
              "type": "string"
            },
            "timeStamp": {
              "format": "date-time",
              "type": "string"
            }
          },
          "required": [
            "message",
            "timeStamp"
          ]
        },
        "CreateCardTemplateDto": {
          "type": "object",
          "properties": {
            "title": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "watermark": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "headerImage": {
              "type": "string",
              "format": "uri"
            },
            "backgroundImage": {
              "type": "string",
              "format": "uri"
            }
          },
          "required": [
            "title",
            "watermark",
            "headerImage",
            "backgroundImage"
          ]
        },
        "UpdateCardTemplateDto": {
          "type": "object",
          "properties": {
            "title": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "watermark": {
              "type": "string",
              "minLength": 1,
              "maxLength": 100
            },
            "headerImage": {
              "type": "string",
              "format": "uri"
            },
            "backgroundImage": {
              "type": "string",
              "format": "uri"
            }
          }
        },
        "CreateEnrollmentDto": {
          "type": "object",
          "properties": {
            "studentId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "classSectionId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "type": {
              "type": "string",
              "enum": [
                "ADMISSION",
                "TRANSFER_IN",
                "REPEATING",
                "PROMOTION"
              ]
            },
            "status": {
              "type": "string",
              "enum": [
                "ACTIVE",
                "EXPELLED",
                "GRADUATED",
                "DECEASED",
                "COMPLETED",
                "WITHDRAWN"
              ]
            },
            "date": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            }
          },
          "required": [
            "studentId",
            "classSectionId",
            "type",
            "status",
            "date"
          ]
        },
        "AssignClassSectionExamDto": {
          "type": "object",
          "properties": {
            "classSectionIds": {
              "type": "array",
              "items": {
                "type": "string",
                "minLength": 36,
                "maxLength": 36
              }
            }
          },
          "required": [
            "classSectionIds"
          ]
        },
        "ClassSectionExamResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "id": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "exam": {
                  "type": "object",
                  "properties": {
                    "name": {
                      "type": "string",
                      "maxLength": 100
                    },
                    "startDate": {
                      "type": "string",
                      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                    },
                    "endDate": {
                      "type": "string",
                      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                    },
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "createdAt": {}
                  },
                  "required": [
                    "name",
                    "startDate",
                    "endDate",
                    "id",
                    "createdAt"
                  ]
                },
                "classSection": {
                  "type": "object",
                  "properties": {
                    "name": {
                      "type": "string",
                      "minLength": 2,
                      "maxLength": 70,
                      "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                    },
                    "isActive": {
                      "type": "boolean",
                      "default": true
                    },
                    "id": {
                      "type": "string"
                    },
                    "totalStudents": {
                      "type": "number"
                    },
                    "createdAt": {},
                    "class": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    },
                    "classTeacher": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "name",
                    "id",
                    "totalStudents",
                    "createdAt",
                    "class",
                    "classTeacher"
                  ]
                },
                "createdAt": {
                  "description": "Date when the exam was assigned to the class section"
                }
              },
              "required": [
                "id",
                "exam",
                "classSection",
                "createdAt"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "ListClassSectionExamsResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "exam": {
                        "type": "object",
                        "properties": {
                          "name": {
                            "type": "string",
                            "maxLength": 100
                          },
                          "startDate": {
                            "type": "string",
                            "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                          },
                          "endDate": {
                            "type": "string",
                            "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                          },
                          "id": {
                            "type": "string",
                            "minLength": 36,
                            "maxLength": 36
                          },
                          "createdAt": {}
                        },
                        "required": [
                          "name",
                          "startDate",
                          "endDate",
                          "id",
                          "createdAt"
                        ]
                      },
                      "classSection": {
                        "type": "object",
                        "properties": {
                          "name": {
                            "type": "string",
                            "minLength": 2,
                            "maxLength": 70,
                            "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                          },
                          "isActive": {
                            "type": "boolean",
                            "default": true
                          },
                          "id": {
                            "type": "string"
                          },
                          "totalStudents": {
                            "type": "number"
                          },
                          "createdAt": {},
                          "class": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          },
                          "classTeacher": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          }
                        },
                        "required": [
                          "name",
                          "id",
                          "totalStudents",
                          "createdAt",
                          "class",
                          "classTeacher"
                        ]
                      },
                      "createdAt": {
                        "description": "Date when the exam was assigned to the class section"
                      }
                    },
                    "required": [
                      "id",
                      "exam",
                      "classSection",
                      "createdAt"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateClassSectionExamDto": {
          "type": "object",
          "properties": {
            "classSectionIds": {
              "type": "array",
              "items": {
                "type": "string",
                "minLength": 36,
                "maxLength": 36
              }
            }
          },
          "required": [
            "classSectionIds"
          ]
        },
        "CreateExamScheduleDto": {
          "type": "object",
          "properties": {
            "date": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "startTime": {
              "type": "string",
              "format": "date-time"
            },
            "endTime": {
              "type": "string",
              "format": "date-time"
            },
            "sectionSubjectId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "totalMarks": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            },
            "passingMarks": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            }
          },
          "required": [
            "date",
            "startTime",
            "endTime",
            "sectionSubjectId",
            "totalMarks",
            "passingMarks"
          ]
        },
        "ExamScheduleResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "date": {
                  "type": "string",
                  "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                },
                "startTime": {
                  "type": "string",
                  "format": "date-time"
                },
                "endTime": {
                  "type": "string",
                  "format": "date-time"
                },
                "totalMarks": {
                  "description": "Total marks for the exam",
                  "type": "number"
                },
                "passingMarks": {
                  "description": "Passing marks for the exam",
                  "type": "number"
                },
                "id": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "exam": {
                  "type": "object",
                  "properties": {
                    "name": {
                      "type": "string",
                      "maxLength": 100
                    },
                    "startDate": {
                      "type": "string",
                      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                    },
                    "endDate": {
                      "type": "string",
                      "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                    },
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "createdAt": {}
                  },
                  "required": [
                    "name",
                    "startDate",
                    "endDate",
                    "id",
                    "createdAt"
                  ]
                },
                "classSection": {
                  "type": "object",
                  "properties": {
                    "name": {
                      "type": "string",
                      "minLength": 2,
                      "maxLength": 70,
                      "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                    },
                    "isActive": {
                      "type": "boolean",
                      "default": true
                    },
                    "id": {
                      "type": "string"
                    },
                    "totalStudents": {
                      "type": "number"
                    },
                    "createdAt": {},
                    "class": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    },
                    "classTeacher": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "name",
                    "id",
                    "totalStudents",
                    "createdAt",
                    "class",
                    "classTeacher"
                  ]
                },
                "subject": {
                  "description": "Subject information",
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "description": "Name of the subject",
                      "type": "string"
                    },
                    "teacher": {
                      "description": "Subject teacher information",
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string",
                          "minLength": 36,
                          "maxLength": 36
                        },
                        "name": {
                          "description": "Name of the subject teacher",
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "teacher"
                  ]
                }
              },
              "required": [
                "date",
                "startTime",
                "endTime",
                "totalMarks",
                "passingMarks",
                "id",
                "exam",
                "classSection",
                "subject"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "ListExamSchedulesResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "date": {
                        "type": "string",
                        "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                      },
                      "startTime": {
                        "type": "string",
                        "format": "date-time"
                      },
                      "endTime": {
                        "type": "string",
                        "format": "date-time"
                      },
                      "totalMarks": {
                        "description": "Total marks for the exam",
                        "type": "number"
                      },
                      "passingMarks": {
                        "description": "Passing marks for the exam",
                        "type": "number"
                      },
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "exam": {
                        "type": "object",
                        "properties": {
                          "name": {
                            "type": "string",
                            "maxLength": 100
                          },
                          "startDate": {
                            "type": "string",
                            "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                          },
                          "endDate": {
                            "type": "string",
                            "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
                          },
                          "id": {
                            "type": "string",
                            "minLength": 36,
                            "maxLength": 36
                          },
                          "createdAt": {}
                        },
                        "required": [
                          "name",
                          "startDate",
                          "endDate",
                          "id",
                          "createdAt"
                        ]
                      },
                      "classSection": {
                        "type": "object",
                        "properties": {
                          "name": {
                            "type": "string",
                            "minLength": 2,
                            "maxLength": 70,
                            "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                          },
                          "isActive": {
                            "type": "boolean",
                            "default": true
                          },
                          "id": {
                            "type": "string"
                          },
                          "totalStudents": {
                            "type": "number"
                          },
                          "createdAt": {},
                          "class": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          },
                          "classTeacher": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          }
                        },
                        "required": [
                          "name",
                          "id",
                          "totalStudents",
                          "createdAt",
                          "class",
                          "classTeacher"
                        ]
                      },
                      "subject": {
                        "description": "Subject information",
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "string",
                            "minLength": 36,
                            "maxLength": 36
                          },
                          "name": {
                            "description": "Name of the subject",
                            "type": "string"
                          },
                          "teacher": {
                            "description": "Subject teacher information",
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string",
                                "minLength": 36,
                                "maxLength": 36
                              },
                              "name": {
                                "description": "Name of the subject teacher",
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          }
                        },
                        "required": [
                          "id",
                          "name",
                          "teacher"
                        ]
                      }
                    },
                    "required": [
                      "date",
                      "startTime",
                      "endTime",
                      "totalMarks",
                      "passingMarks",
                      "id",
                      "exam",
                      "classSection",
                      "subject"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateExamScheduleDto": {
          "type": "object",
          "properties": {
            "date": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "startTime": {
              "type": "string",
              "format": "date-time"
            },
            "endTime": {
              "type": "string",
              "format": "date-time"
            },
            "sectionSubjectId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "totalMarks": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            },
            "passingMarks": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false
            }
          }
        },
        "CreateExamResultDto": {
          "type": "object",
          "properties": {
            "enrollmentId": {
              "type": "string",
              "minLength": 36,
              "maxLength": 36
            },
            "marksObtained": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false,
              "nullable": true
            },
            "remarks": {
              "type": "string",
              "nullable": true
            },
            "isAbsent": {
              "type": "boolean",
              "default": false
            }
          },
          "required": [
            "enrollmentId",
            "marksObtained"
          ]
        },
        "ExamResultResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "marksObtained": {
                  "type": "number",
                  "minimum": 0,
                  "exclusiveMinimum": true,
                  "maximum": 1000,
                  "exclusiveMaximum": false,
                  "nullable": true
                },
                "remarks": {
                  "type": "string",
                  "nullable": true
                },
                "isAbsent": {
                  "type": "boolean",
                  "default": false
                },
                "id": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36
                },
                "student": {
                  "description": "Student information",
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string",
                      "minLength": 36,
                      "maxLength": 36
                    },
                    "name": {
                      "description": "Name of the student",
                      "type": "string"
                    },
                    "rollNumber": {
                      "description": "Roll number of the student",
                      "type": "number"
                    }
                  },
                  "required": [
                    "id",
                    "name",
                    "rollNumber"
                  ]
                },
                "classSection": {
                  "type": "object",
                  "properties": {
                    "name": {
                      "type": "string",
                      "minLength": 2,
                      "maxLength": 70,
                      "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                    },
                    "isActive": {
                      "type": "boolean",
                      "default": true
                    },
                    "id": {
                      "type": "string"
                    },
                    "totalStudents": {
                      "type": "number"
                    },
                    "createdAt": {},
                    "class": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    },
                    "classTeacher": {
                      "type": "object",
                      "properties": {
                        "id": {
                          "type": "string"
                        },
                        "name": {
                          "type": "string"
                        }
                      },
                      "required": [
                        "id",
                        "name"
                      ]
                    }
                  },
                  "required": [
                    "name",
                    "id",
                    "totalStudents",
                    "createdAt",
                    "class",
                    "classTeacher"
                  ]
                },
                "createdAt": {
                  "description": "Date when the exam result was created"
                }
              },
              "required": [
                "marksObtained",
                "id",
                "student",
                "classSection",
                "createdAt"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "ListExamResultsResponseDto": {
          "type": "object",
          "properties": {
            "statusCode": {
              "type": "number"
            },
            "message": {
              "type": "string"
            },
            "data": {
              "type": "object",
              "properties": {
                "items": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "marksObtained": {
                        "type": "number",
                        "minimum": 0,
                        "exclusiveMinimum": true,
                        "maximum": 1000,
                        "exclusiveMaximum": false,
                        "nullable": true
                      },
                      "remarks": {
                        "type": "string",
                        "nullable": true
                      },
                      "isAbsent": {
                        "type": "boolean",
                        "default": false
                      },
                      "id": {
                        "type": "string",
                        "minLength": 36,
                        "maxLength": 36
                      },
                      "student": {
                        "description": "Student information",
                        "type": "object",
                        "properties": {
                          "id": {
                            "type": "string",
                            "minLength": 36,
                            "maxLength": 36
                          },
                          "name": {
                            "description": "Name of the student",
                            "type": "string"
                          },
                          "rollNumber": {
                            "description": "Roll number of the student",
                            "type": "number"
                          }
                        },
                        "required": [
                          "id",
                          "name",
                          "rollNumber"
                        ]
                      },
                      "classSection": {
                        "type": "object",
                        "properties": {
                          "name": {
                            "type": "string",
                            "minLength": 2,
                            "maxLength": 70,
                            "pattern": "^[a-zA-Z0-9 .,'&()-]+$"
                          },
                          "isActive": {
                            "type": "boolean",
                            "default": true
                          },
                          "id": {
                            "type": "string"
                          },
                          "totalStudents": {
                            "type": "number"
                          },
                          "createdAt": {},
                          "class": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          },
                          "classTeacher": {
                            "type": "object",
                            "properties": {
                              "id": {
                                "type": "string"
                              },
                              "name": {
                                "type": "string"
                              }
                            },
                            "required": [
                              "id",
                              "name"
                            ]
                          }
                        },
                        "required": [
                          "name",
                          "id",
                          "totalStudents",
                          "createdAt",
                          "class",
                          "classTeacher"
                        ]
                      },
                      "createdAt": {
                        "description": "Date when the exam result was created"
                      }
                    },
                    "required": [
                      "marksObtained",
                      "id",
                      "student",
                      "classSection",
                      "createdAt"
                    ]
                  }
                },
                "total": {
                  "type": "number"
                }
              },
              "required": [
                "items",
                "total"
              ]
            }
          },
          "required": [
            "statusCode",
            "message",
            "data"
          ]
        },
        "UpdateExamResultDto": {
          "type": "object",
          "properties": {
            "marksObtained": {
              "type": "number",
              "minimum": 0,
              "exclusiveMinimum": true,
              "maximum": 1000,
              "exclusiveMaximum": false,
              "nullable": true
            },
            "remarks": {
              "type": "string",
              "nullable": true
            },
            "isAbsent": {
              "type": "boolean",
              "default": false
            }
          }
        }
      }
    }
  },
  "customOptions": {
    "persistAuthorization": true,
    "docExpansion": "none",
    "filter": true,
    "showExtensions": true,
    "showCommonExtensions": true,
    "deepLinking": true,
    "displayOperationId": false,
    "displayRequestDuration": true,
    "tryItOutEnabled": false
  }
};
  url = options.swaggerUrl || url
  let urls = options.swaggerUrls
  let customOptions = options.customOptions
  let spec1 = options.swaggerDoc
  let swaggerOptions = {
    spec: spec1,
    url: url,
    urls: urls,
    dom_id: '#swagger-ui',
    deepLinking: true,
    presets: [
      SwaggerUIBundle.presets.apis,
      SwaggerUIStandalonePreset
    ],
    plugins: [
      SwaggerUIBundle.plugins.DownloadUrl
    ],
    layout: "StandaloneLayout"
  }
  for (let attrname in customOptions) {
    swaggerOptions[attrname] = customOptions[attrname];
  }
  let ui = SwaggerUIBundle(swaggerOptions)

  if (customOptions.initOAuth) {
    ui.initOAuth(customOptions.initOAuth)
  }

  if (customOptions.authAction) {
    ui.authActions.authorize(customOptions.authAction)
  }
  
  window.ui = ui
}
