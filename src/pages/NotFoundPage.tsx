import { Home, ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router";

export const NotFoundPage = () => {
  // Function to handle going back to the previous page
  const navigate = useNavigate();
  const handleGoBack = async () => {
    await navigate(-1);
  };

  // Function to handle going to the home page
  const handleGoHome = async () => {
    await navigate("/");
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-base-100 text-base-content p-4">
      {/* Main content container */}
      <div className="max-w-md w-full bg-base-200 shadow-lg rounded-lg p-8 text-center">
        {/* Error code */}
        <h1 className="text-8xl font-bold text-primary mb-4">404</h1>

        {/* Error title */}
        <h2 className="text-2xl font-semibold mb-2">Page Not Found</h2>

        {/* Error description */}
        <p className="text-base-content/70 mb-8">
          The page you're looking for doesn't exist or has been moved.
        </p>

        {/* Divider */}
        <div className="divider my-6"></div>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <button
            onClick={handleGoBack}
            className="btn btn-outline flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            Go Back
          </button>

          <button
            onClick={handleGoHome}
            className="btn btn-primary flex items-center gap-2"
          >
            <Home size={18} />
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
};
