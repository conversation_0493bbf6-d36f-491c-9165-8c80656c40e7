import { FullPageLoader } from "@/common/components/ui/FullPageLoader";
import { useUserProfile } from "@/core/user/user-query";
import { Header } from "@/features/dashboard/components/header/Header";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { isBranchAdmin } from "@/utils/user-roles.utils";
import { Navigate, Outlet } from "react-router";

const AppLayout = () => {
  const { data: user, isFetching } = useUserProfile();

  const { selectedBranch, setSelectedBranch } = useBranchStore();

  if (isFetching) {
    return (
      <div>
        <FullPageLoader />
      </div>
    );
  }

  if (!selectedBranch && isBranchAdmin(user)) {
    setSelectedBranch(user.branch);
  }

  return user.isPasswordTemporary ? (
    <Navigate to="/set-password" />
  ) : (
    <div className="flex flex-col min-h-screen pt-[72px]">
      <Header />
      <Outlet />
    </div>
  );
};

export default AppLayout;
