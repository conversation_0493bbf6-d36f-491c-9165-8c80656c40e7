export const config = {
  api: {
    baseUrl:
      (import.meta.env.VITE_API_BASE_URL as string | undefined) ??
      "http://localhost:3000/api/v1",
  },
  auth: {
    tokenName: "jwt_token",
  },

  endpoint: {
    subjects: {
      add: (sessionId: string) => `/sessions/${sessionId}/subjects`,
      list: (sessionId: string) => `/sessions/${sessionId}/subjects`,
      update: (sessionId: string, subjectId: string) =>
        `/sessions/${sessionId}/subjects/${subjectId}`,
    },
  },
} as const;
