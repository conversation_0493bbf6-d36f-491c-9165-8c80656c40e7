import { sendApiRequest } from "@/common/services/api.service";
import type { RegisterUserData, SignInUserData } from "../types/auth.type";
import { logger } from "@/lib/logger";

type SignInResponse = {
  accessToken: string;
  role: number;
};

export type UserProfile = Omit<RegisterUserData, "password">;

export type GetUserProfileResponse = {
  user: UserProfile;
};

export async function signInUser(userData: SignInUserData) {
  try {
    return await sendApiRequest<SignInResponse>("/auth/login", {
      method: "POST",
      withCredentials: true,
      data: userData,
    });
  } catch (error) {
    logger.error("[SignInUserService]: Failed to sign in user");
    throw error;
  }
}

export async function requestPasswordReset(email: string) {
  const response = await sendApiRequest("/auth/request-password-reset", {
    method: "POST",
    data: { email },
  });
  return response;
}

/**
 * Changes user password from temporary credentials to permanent password
 * @param data The password change form data
 */
export async function setPassword(newPassword: string) {
  try {
    return await sendApiRequest("/auth/set-password", {
      method: "POST",
      data: { password: newPassword },
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.error("[SetPasswordService]: Failed to set password", error);
    throw error;
  }
}
