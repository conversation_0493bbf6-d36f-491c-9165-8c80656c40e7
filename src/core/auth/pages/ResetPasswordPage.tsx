import { useState } from "react";
import { useForm, type SubmitH<PERSON><PERSON> } from "react-hook-form";
import { useNavigate } from "react-router";
import { useDispatch } from "react-redux";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { authActions } from "@/core/auth/auth.slice";
import { notify } from "@/lib/notify";
import { InputField } from "@/common/components/ui/form/InputField";
import { Button } from "@/common/components/ui/Button";
import { FaEye, FaEyeSlash, FaLock } from "react-icons/fa";
import { motion } from "framer-motion";

import logo from "@/assets/logo.png";
import { updatePassword } from "../services/setPassword.service";

// Schema for password reset
const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character",
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type ResetPasswordData = z.infer<typeof resetPasswordSchema>;

export const ResetPasswordPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting: pending },
  } = useForm<ResetPasswordData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prev) => !prev);
  };

  const onSubmit: SubmitHandler<ResetPasswordData> = async (data) => {
    try {
      await updatePassword(data.password);

      notify.success("Password reset successful");

      // Update auth state to indicate password has been changed
      dispatch(authActions.updatePasswordStatus({ passwordChanged: true }));

      await navigate("/");
    } catch (error) {
      if (error instanceof Error) {
        notify.error(error.message);
      } else {
        notify.error("An unexpected error occurred");
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="grid md:grid-cols-2 w-full max-w-5xl bg-base-100 rounded-lg shadow-md overflow-hidden">
        {/* Left Side - Illustration/Branding */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="hidden md:flex flex-col justify-center items-center p-8 bg-gray-300 text-base-content"
        >
          <div className="text-center">
            <img
              src={logo}
              alt="E-Bridge Logo"
              className="w-32 h-32 mx-auto mb-6"
            />
            <h1 className="text-4xl font-bold mb-4">E-Bridge</h1>
            <p className="text-xl mb-6">School Admin Panel</p>
            <div className="space-y-4 text-left">
              <div className="flex items-center">
                <div className="bg-base-300 p-2 rounded-full mr-3">
                  <FaLock className="text-base-content text-xl" />
                </div>
                <p>Set up a strong password for your account</p>
              </div>
              <div className="flex items-center">
                <div className="bg-base-300 p-2 rounded-full mr-3">
                  <FaLock className="text-base-content text-xl" />
                </div>
                <p>Secure your school management access</p>
              </div>
              <div className="flex items-center">
                <div className="bg-base-300 p-2 rounded-full mr-3">
                  <FaLock className="text-base-content text-xl" />
                </div>
                <p>One-time setup to protect your account</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Right Side - Reset Password Form */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="p-8 md:p-12 flex flex-col justify-center bg-base-200"
        >
          {/* Mobile Logo */}
          <div className="md:hidden flex flex-col items-center mb-8">
            <img src={logo} alt="E-Bridge Logo" className="w-20 h-20 mb-2" />
            <h1 className="text-2xl font-bold">E-Bridge</h1>
            <p className="text-sm text-gray-500">School Admin Panel</p>
          </div>

          <h2 className="text-2xl md:text-3xl font-bold mb-2">
            Reset Your Password
          </h2>
          <p className="text-gray-500 mb-8">
            Create a new secure password for your account
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-6 bg-white p-4 rounded-lg shadow-md"
            noValidate
          >
            {/* New Password Input */}
            <div className="relative">
              <InputField
                type={showPassword ? "text" : "password"}
                label="New Password"
                placeholder="Enter new password"
                errorMessage={errors.password?.message}
                name="password"
                register={register}
              >
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-800"
                >
                  {showPassword ? (
                    <FaEyeSlash size={18} />
                  ) : (
                    <FaEye size={18} />
                  )}
                </button>
              </InputField>
            </div>

            {/* Confirm Password Input */}
            <div className="relative">
              <InputField
                type={showConfirmPassword ? "text" : "password"}
                label="Confirm Password"
                placeholder="Confirm your password"
                errorMessage={errors.confirmPassword?.message}
                name="confirmPassword"
                register={register}
              >
                <button
                  type="button"
                  onClick={toggleConfirmPasswordVisibility}
                  className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-800"
                >
                  {showConfirmPassword ? (
                    <FaEyeSlash size={18} />
                  ) : (
                    <FaEye size={18} />
                  )}
                </button>
              </InputField>
            </div>

            {/* Password Requirements */}
            <div className="text-xs text-gray-500 space-y-1">
              <p>Password requirements:</p>
              <ul className="list-disc pl-5">
                <li>At least 8 characters</li>
                <li>Include uppercase and lowercase letters</li>
                <li>Include at least one number</li>
                <li>Include at least one special character (@$!%*?&)</li>
              </ul>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              shape="primary"
              pending={pending}
              className="w-full"
            >
              Reset Password
            </Button>
          </form>

          {/* Footer */}
          <p className="text-center text-sm text-gray-500 mt-8">
            © {new Date().getFullYear()} E-Bridge School Management System
          </p>
        </motion.div>
      </div>
    </div>
  );
};
