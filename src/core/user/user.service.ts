import { sendApiRequest } from "@/common/services/api.service";
import type { UserProfile } from "./types/user.type";
import { logger } from "@/lib/logger";

export const fetchUserProfile = async () => {
  try {
    return await sendApiRequest<UserProfile>("/users/me", {
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.info("Failed to fetch user profile", error);
    throw error;
  }
};
