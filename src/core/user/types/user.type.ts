import type { Role } from "@/common/constants/roles.constants";
import type { Branch } from "@/features/onboarding/setup/branches/branch.type";
import type { Institute } from "@/features/onboarding/setup/Institute/institute.type";

interface BaseUser {
  id: string;
  name: string;
  email: string;
  role: Role;
  gender: "MALE" | "FEMALE" | "OTHER";
  cnic: string;
  isActive: boolean;
  isPasswordTemporary: boolean;
  address: string;
  photo: string | null;
  createdAt: string;
}

export interface InstituteOwner extends BaseUser {
  role: Role.InstituteOwner;
  institute: Institute;
}

export interface BranchAdmin extends BaseUser {
  role: Role.BranchAdmin;
  branch: Branch;
}

export type UserProfile = BranchAdmin | InstituteOwner;
