import {
  type PayloadAction,
  createAsyncThunk,
  createSlice,
} from "@reduxjs/toolkit";
import type { UserProfile } from "./types/user.type";
import { sendApiRequest } from "@/common/services/api.service";
import { logger } from "@/lib/logger";

interface UserState {
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  profile: null,
  loading: false,
  error: null,
};

export const fetchUserProfileThunk = createAsyncThunk<UserProfile>(
  "user/fetchUserProfile",
  async (_, thunkAPI) => {
    try {
      return await sendApiRequest<UserProfile>("/users/me", {
        withAuthorization: true,
      });
    } catch (error: unknown) {
      logger.error(" Failed to fetch user profile", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch user profile";
      return thunkAPI.rejectWithValue(errorMessage);
    }
  },
);

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setProfile: (state, action: PayloadAction<UserProfile>) => {
      state.profile = action.payload;
    },
    clearProfile: (state) => {
      state.profile = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserProfileThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfileThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfileThunk.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const userProfileActions = userSlice.actions;
export const userProfileReducer = userSlice.reducer;
