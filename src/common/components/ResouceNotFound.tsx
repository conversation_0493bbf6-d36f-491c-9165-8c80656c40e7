import { ArrowLeft, Search } from "lucide-react";
import { useNavigate } from "react-router";

interface NotFoundComponentProps {
  resourceName?: string;
  customMessage?: string;
  backUrl?: string;
  onBackClick?: () => void;
}

export const ResourceNotFound: React.FC<NotFoundComponentProps> = ({
  resourceName = "Record",
  customMessage,
  backUrl,
  onBackClick,
}) => {
  const navigate = useNavigate();

  const handleGoBack = async () => {
    if (onBackClick) {
      onBackClick();
    } else if (backUrl) {
      await navigate(backUrl);
    } else {
      await navigate(-1);
    }
  };

  const message =
    customMessage ??
    `The ${resourceName.toLowerCase()} you're looking for could not be found.`;

  return (
    <div className="w-full p-8 flex flex-col items-center justify-center min-h-[60vh] flex-1">
      <div className="max-w-md w-full bg-base-100 rounded-lg shadow-lg overflow-hidden">
        <div className="p-8 flex flex-col items-center text-center">
          <div className="bg-base-200 p-4 rounded-full mb-6">
            <Search className="w-10 h-10 text-base-content/70" />
          </div>

          <h2 className="text-2xl font-semibold mb-2">{`${resourceName} Not Found`}</h2>
          <p className="text-base-content/70 mb-6">{message}</p>

          <button onClick={handleGoBack} className="btn btn-primary gap-2">
            <ArrowLeft size={18} />
            Go Back
          </button>
        </div>

        <div className="bg-base-200 px-8 py-4 text-sm text-center text-base-content/60">
          If you believe this is an error, please contact your administrator.
        </div>
      </div>
    </div>
  );
};
