import { motion } from "framer-motion";
import { Button } from "@/common/components/ui/Button";
import type { ReactNode } from "react";
import { CircleXIcon } from "lucide-react";

export interface ViewRecordModalProps<T> {
  title: string;
  record: T;
  subtitle?: string;
  columns: { label: string; render: (item: T) => ReactNode }[];
  onUpdate: (selectedRecord: T) => void;
  onClose: () => void;
}

export function ViewRecordModal<T>({
  title,
  subtitle,
  record,
  columns,
  onUpdate,
  onClose,
}: ViewRecordModalProps<T>) {
  const handleOnUpdate = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    onUpdate(record);
  };

  return (
    <div className="fixed inset-0 bg-semi-transparent bg-opacity-50 flex justify-center items-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-base-100 rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative"
      >
        <button
          className="absolute cursor-pointer top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
          onClick={onClose}
        >
          <CircleXIcon />
        </button>

        <div className="p-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mt-4">{title}</h2>
            <div className="mt-2">
              <span className={`badge badge-accent badge-lg`}>{subtitle}</span>
            </div>
          </div>

          <div className="divider"></div>

          <div className="grid grid-cols-1 gap-4">
            <div className="bg-base-200 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-3">{`${title} Details`}</h3>
              <table className="w-full">
                <tbody>
                  {columns.map((col) => (
                    <tr key={col.label} className="border-b">
                      <td className="py-2 font-medium">{col.label}</td>
                      <td className="py-2">{col.render(record)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-8 flex justify-center">
            <Button shape="primary" onClick={handleOnUpdate} className="px-6">
              Update
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
