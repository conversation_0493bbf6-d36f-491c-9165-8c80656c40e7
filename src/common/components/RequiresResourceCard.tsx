import { AlertCircle, ArrowLeft, PlusCircle } from "lucide-react";
import { useNavigate } from "react-router";
import React from "react";

interface RequiresResourceProps {
  resourceName?: string;
  customMessage?: string;
  createUrl?: string;
}

export const RequiresResource: React.FC<RequiresResourceProps> = ({
  resourceName = "Resource",
  customMessage,
  createUrl,
}) => {
  const navigate = useNavigate();

  const handleGoBack = async () => {
    await navigate(-1);
  };

  const handleCreate = async () => {
    if (createUrl) {
      await navigate(createUrl);
    }
  };

  const message =
    customMessage ??
    `A ${resourceName.toLowerCase()} is required to continue. Please create it first.`;

  return (
    <div className="w-full min-h-[60vh] flex items-center justify-center px-4 py-10">
      <div className="bg-base-100 max-w-xl w-full rounded-2xl shadow-xl p-8 text-center border  border-warning">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="bg-warning/20 text-warning p-4 rounded-full">
            <AlertCircle className="w-10 h-10" />
          </div>

          <h2 className="text-2xl font-bold text-base-content">
            {resourceName} Required
          </h2>

          <p className="text-base-content/70 text-sm">{message}</p>

          <div className="flex flex-wrap gap-3 mt-4 justify-center">
            <button
              onClick={handleGoBack}
              className="btn btn-outline btn-warning gap-2"
            >
              <ArrowLeft size={18} />
              Go Back
            </button>

            {createUrl && (
              <button onClick={handleCreate} className="btn btn-primary gap-2">
                <PlusCircle size={18} />
                Create {resourceName}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
