import { motion } from "framer-motion";
import { Button } from "./ui/Button";

interface ResourceCreationFormProps {
  children: React.ReactNode;
  isFormSubmitting: boolean;
  isEditMode: boolean;
  onFormSubmit: () => void;
  onCancelEdit: () => void;
  resourceLabel: string;
  styles?: {
    submitButton?: string;
  };
}

export function ResourceCreationForm({
  children,
  isFormSubmitting,
  isEditMode,
  styles,
  onFormSubmit,
  onCancelEdit,
  resourceLabel,
}: ResourceCreationFormProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-base-100 p-6 rounded-lg shadow-md border border-base-300"
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-[#0f243f]">
          {isEditMode ? `Edit ${resourceLabel}` : `Add ${resourceLabel}`}
        </h2>
        {isEditMode && (
          <Button shape="neutral" onClick={onCancelEdit} className="btn-sm">
            Cancel Edit
          </Button>
        )}
      </div>

      <form onSubmit={onFormSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">{children}</div>

        <div className="grid">
          <Button
            shape="primary"
            type="submit"
            pending={isFormSubmitting}
            className={`justify-self-end ${styles?.submitButton ?? ""}`}
          >
            {isEditMode ? `Update ${resourceLabel}` : `Add ${resourceLabel}`}
          </Button>
        </div>
      </form>
    </motion.div>
  );
}
