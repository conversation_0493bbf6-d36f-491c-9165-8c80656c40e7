import { useState } from "react";
import { AlertCircle, ArrowLeft, Book, Mail, HelpCircle } from "lucide-react";

interface ErrorComponentProps {
  title?: string;
  message?: string;
  error?: Error;
  onRetry?: () => void;
  onBack?: () => void;
}

export function ErrorComponent({
  title = "Something went wrong",
  message = "We're sorry, but we encountered an unexpected error.",
  error,
  onRetry,
  onBack,
}: ErrorComponentProps) {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-base-100 rounded-lg shadow-xl overflow-hidden">
        <div className="p-8">
          <div className="flex flex-col items-center text-center mb-6">
            <div className="bg-error bg-opacity-20 p-3 rounded-full mb-4">
              <AlertCircle className="text-error w-8 h-8" />
            </div>
            <h2 className="text-2xl font-bold text-base-content">{title}</h2>
            <p className="mt-2 text-base-content/70">{message}</p>
          </div>

          <div className="space-y-4">
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={
                  onRetry ??
                  (() => {
                    window.location.reload();
                  })
                }
                className="btn btn-primary flex-1"
              >
                Retry
              </button>

              {onBack && (
                <button
                  onClick={onBack}
                  className="btn btn-outline flex-1 gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Go Back
                </button>
              )}
            </div>

            {/* Error Details */}
            {error && (
              <div className="mt-6">
                <div
                  className="flex items-center justify-between cursor-pointer text-sm text-base-content/70 hover:text-base-content"
                  onClick={() => {
                    setShowDetails(!showDetails);
                  }}
                >
                  <span>Technical Details</span>
                  <span>{showDetails ? "−" : "+"}</span>
                </div>

                {showDetails && (
                  <div className="mt-2 p-3 bg-base-200 rounded-md overflow-auto text-sm font-mono">
                    <p className="text-error">
                      {error.name}: {error.message}
                    </p>
                    <pre className="mt-2 text-xs whitespace-pre-wrap break-words text-base-content/70">
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-8 py-6 text-center border-t border-base-300">
          <div className="flex flex-col md:flex-row items-center justify-center gap-4">
            <div className="flex gap-3">
              <button className="btn btn-circle btn-sm btn-ghost">
                <a
                  href="https://docs.example.com"
                  className="text-base-content/70 hover:text-primary"
                >
                  <Book size={16} />
                </a>
              </button>
              <button className="btn btn-circle btn-sm btn-ghost">
                <a
                  href="mailto:<EMAIL>"
                  className="text-base-content/70 hover:text-primary"
                >
                  <Mail size={16} />
                </a>
              </button>
              <button className="btn btn-circle btn-sm btn-ghost">
                <a
                  href="https://support.example.com"
                  className="text-base-content/70 hover:text-primary"
                >
                  <HelpCircle size={16} />
                </a>
              </button>
            </div>
            <p className="text-sm text-base-content/60">
              Need help?{" "}
              <a href="#" className="text-primary hover:underline">
                Visit our support center
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
