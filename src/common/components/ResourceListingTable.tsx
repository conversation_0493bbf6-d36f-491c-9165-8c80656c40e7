import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { Button } from "./ui/Button";
import { type ReactNode, useEffect, useState } from "react";
import type { PaginationParams } from "../types/global.types";

type ResourceListingTableProps<T> = {
  records?: T[];
  totalRecords?: number;
  isDataLoading: boolean;
  searchLabel: string;
  searchFieldValue: (record: T) => string;
  error?: Error | null;
  pagination: PaginationParams;
  onPaginationChange: (paginationParams: PaginationParams) => void;
  columns: { label: string; render: (item: T) => ReactNode }[];
  onView: (selectedRecord: T) => void;
  actionButtonText?: string;
};

export function ResourceListingTable<T>({
  isDataLoading,
  searchLabel,
  error,
  searchFieldValue,
  columns,
  totalRecords = 0,
  onView,
  pagination,
  onPaginationChange,
  records,
  actionButtonText = "View",
}: ResourceListingTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredRecords, setFilteredRecords] = useState<T[]>([]);
  // Handle pagination
  const handlePageChange = (newPage: number) => {
    onPaginationChange({
      ...pagination,
      offset: (newPage - 1) * pagination.limit,
    });
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalRecords / pagination.limit);
  const currentPage = Math.floor(pagination.offset / pagination.limit) + 1;
  useEffect(() => {
    if (!records?.length) {
      setFilteredRecords([]);
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredRecords(records);
      return;
    }

    let items = [...records];
    if (searchTerm) {
      const lower = searchTerm.toLowerCase();
      items = items.filter((item) => {
        const field = searchFieldValue(item);
        return field.toLowerCase().includes(lower);
      });
    }

    setFilteredRecords(items);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [records, searchTerm]);

  if (error) {
    return (
      <div className="bg-error/10 border border-error text-error p-4 rounded-lg">
        <p className="font-semibold">Error:</p>
        <p>{error.message}</p>
        <button
          onClick={() => {
            location.reload();
          }}
          className="btn btn-primary mt-4"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      {/* Search bar at the top left */}
      <div className="p-4 bg-base-200 border-b flex items-center">
        <div className="flex items-center gap-4 w-1/3">
          <div className="relative w-full">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              className="input input-bordered input-sm w-full pr-10"
              placeholder={`Search by ${searchLabel}...`}
            />
            <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          {searchTerm && (
            <Button
              shape="neutral"
              onClick={() => {
                setSearchTerm("");
              }}
              className="btn-xs"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse table-fixed">
          <thead className="bg-[#0f243f] text-white">
            <tr>
              <th className="w-[40px] text-left p-3">#</th>
              {columns.map((col, i) => {
                return (
                  <th key={i} className="p-3 text-center">
                    {col.label}
                  </th>
                );
              })}
              <th className="w-[15%] py-3 px-3 text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            {isDataLoading ? (
              <tr>
                <td colSpan={columns.length + 2} className="text-center py-8">
                  <div className="loading loading-spinner loading-lg text-primary mx-auto"></div>
                </td>
              </tr>
            ) : filteredRecords.length > 0 ? (
              filteredRecords.map((record, index) => (
                <tr key={index} className="hover:bg-gray-50 ">
                  <td className="p-3   text-left font-medium">
                    {pagination.offset + index + 1}
                  </td>
                  {columns.map((col, colIdx) => (
                    <td key={colIdx} className="py-3 px-3 text-center truncate">
                      {col.render(record)}
                    </td>
                  ))}
                  <td className="py-3 px-3 text-center">
                    <button
                      className="btn btn-sm btn-ghost text-primary"
                      onClick={() => {
                        onView(record);
                      }}
                    >
                      {actionButtonText}
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length + 2}
                  className="text-center py-8 text-gray-500"
                >
                  No records found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination - Always visible */}
      <div className="flex flex-col items-center py-4 border-t">
        <div className="flex items-center justify-between w-full px-4 mb-2 text-sm text-gray-500">
          <div>
            Page {currentPage} of {totalPages || 1}
          </div>
          <div>
            Showing {filteredRecords.length > 0 ? pagination.offset + 1 : 0} -{" "}
            {Math.min(pagination.offset + filteredRecords.length, totalRecords)}{" "}
            of {totalRecords} subjects
          </div>
        </div>

        <div className="join shadow-sm">
          <button
            className="join-item btn btn-sm bg-base-200 hover:bg-base-300"
            onClick={() => {
              handlePageChange(1);
            }}
            disabled={currentPage === 1}
            aria-label="First page"
          >
            «
          </button>
          <button
            className="join-item btn btn-sm bg-base-200 hover:bg-base-300"
            onClick={() => {
              handlePageChange(currentPage - 1);
            }}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            ‹
          </button>

          {/* Page numbers */}
          {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
            // Show pages around current page
            let pageNum = currentPage;
            if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }

            if (pageNum > 0 && pageNum <= totalPages) {
              return (
                <button
                  key={pageNum}
                  className={`join-item btn btn-sm min-w-[2.5rem] ${
                    currentPage === pageNum
                      ? "bg-primary text-primary-content hover:bg-primary-focus"
                      : "bg-base-200 hover:bg-base-300"
                  }`}
                  onClick={() => {
                    handlePageChange(pageNum);
                  }}
                  aria-label={`Page ${pageNum}`}
                  aria-current={currentPage === pageNum ? "page" : undefined}
                >
                  {pageNum}
                </button>
              );
            }
            return null;
          })}

          <button
            className="join-item btn btn-sm bg-base-200 hover:bg-base-300"
            onClick={() => {
              handlePageChange(currentPage + 1);
            }}
            disabled={currentPage === totalPages}
            aria-label="Next page"
          >
            ›
          </button>
          <button
            className="join-item btn btn-sm bg-base-200 hover:bg-base-300"
            onClick={() => {
              handlePageChange(totalPages);
            }}
            disabled={currentPage === totalPages}
            aria-label="Last page"
          >
            »
          </button>
        </div>
      </div>
    </div>
  );
}
