import { type InputHTMLAttributes, useState } from "react";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>P<PERSON>,
  <PERSON>V<PERSON><PERSON>,
  UseFormRegister,
} from "react-hook-form";
import { FaEye, FaEyeSlash } from "react-icons/fa";

type Props<T extends FieldValues> = {
  name: FieldPath<T>;
  label: string;
  errorMessage?: FieldError["message"];
  register: UseFormRegister<T>;
  valueAsNumber?: boolean;
  className?: string;
  children?: React.ReactNode;
} & InputHTMLAttributes<HTMLInputElement>;

export function InputField<T extends FieldValues = FieldValues>({
  name,
  label,
  errorMessage,
  register,
  valueAsNumber,
  children,
  className = "relative left-0.5",
  type,
  ...rest
}: Props<T>) {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const getInputType = () => {
    if (type === "password") {
      return showPassword ? "text" : "password";
    }
    return type;
  };

  return (
    <div className={`form-control w-full ${className}`}>
      {/* Label */}
      <label className="label font-medium text-sm">
        <span className="label-text text-base-content ">{label}</span>
      </label>
      <div className="relative">
        <input
          className={`input input-bordered relative w-full bg-base-100 text-base-content focus:outline-none focus:ring-2 focus:ring-info ${errorMessage ? "border-red-500" : ""}`}
          type={getInputType()}
          {...register(name, { valueAsNumber })}
          {...rest}
        />
        {children}
        {type === "password" && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-800"
          >
            {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
          </button>
        )}
      </div>
      {errorMessage && (
        <span className="text-error text-xs mt-1">{errorMessage}</span>
      )}
    </div>
  );
}
