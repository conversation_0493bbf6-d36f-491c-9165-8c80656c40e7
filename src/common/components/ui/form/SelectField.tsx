import { capitalizeFirstLetter } from "@/utils/utils";
import type { SelectHTMLAttributes } from "react";
import type {
  FieldError,
  FieldPath,
  FieldValues,
  UseFormRegister,
} from "react-hook-form";

type Props<T extends FieldValues> = {
  name: FieldPath<T>;
  label: string;
  errorMessage?: FieldError["message"];
  register?: UseFormRegister<T>;
  options?: { value: string | number; label: string; selected?: boolean }[];
  valueAsNumber?: boolean;
  className?: string;
  defaultOptionLabel?: string;
} & SelectHTMLAttributes<HTMLSelectElement>;

export const SelectField = <T extends FieldValues>({
  name,
  label,
  errorMessage,
  register,
  options,
  valueAsNumber,
  defaultOptionLabel,
  className = "relative left-0.5",
  ...rest
}: Props<T>) => {
  return (
    <div className={`form-control w-full ${className}`}>
      {/* Label */}
      {label && (
        <label className="label font-medium text-sm">
          <span className="label-text text-base-content ">{label}</span>
        </label>
      )}
      <div className="relative">
        <select
          className={`select w-full bg-base-100 text-base-content focus:outline-none focus:ring-2 focus:ring-info ${errorMessage ? "border-red-500" : ""}`}
          {...(register ? register(name, { valueAsNumber }) : {})}
          {...rest}
        >
          <option value="">
            {defaultOptionLabel ??
              `-- Select ${capitalizeFirstLetter(label)} --`}
          </option>
          {options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      {errorMessage && (
        <span className="text-error text-xs mt-1">{errorMessage}</span>
      )}
    </div>
  );
};
