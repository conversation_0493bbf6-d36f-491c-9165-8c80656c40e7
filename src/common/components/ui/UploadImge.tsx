import { useState, useRef, useEffect } from "react";
import { logger } from "@/lib/logger";
import { getProfilePlaceholderImage } from "@/utils/image.utils";

interface UploadImageProps {
  onImageSelected: (file: File) => void;
  className?: string;
  buttonText?: string;
  previewSize?: "small" | "medium" | "large";
  initialPreview?: string;
  accept?: string;
  maxSizeInMB?: number;
}

export const UploadImage = ({
  onImageSelected,
  initialPreview = "",
  className = "",
  buttonText = "Choose Image",
  previewSize = "medium",
  accept = "image/*",
  maxSizeInMB = 5,
}: UploadImageProps) => {
  const [previewUrl, setPreviewUrl] = useState<string>(initialPreview);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Clean up object URLs on unmount to avoid memory leaks
  useEffect(() => {
    return () => {
      if (previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Update preview if initialPreview changes
  useEffect(() => {
    if (initialPreview) {
      setPreviewUrl(initialPreview);
    }
  }, [initialPreview]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (!files || files.length === 0) {
      logger.warn("No files selected");
      return;
    }

    const file = files[0];
    if (!file) {
      logger.warn("File is null or undefined");
      return;
    }

    // Validate file size
    if (file.size > maxSizeInMB * 1024 * 1024) {
      logger.error(`File too large (max ${maxSizeInMB}MB)`);
      alert(`File is too large. Maximum size is ${maxSizeInMB}MB.`);
      return;
    }

    // Clean up previous preview if exists
    if (previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }

    // Clean up previous preview if exists
    if (previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }

    // Create object URL for preview
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Pass the file to parent component
    setIsLoading(true);
    try {
      onImageSelected(file);
    } finally {
      setIsLoading(false);
    }

    // Reset the input value so the same file can be selected again if needed
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Determine preview size class
  const previewSizeClass = {
    small: "w-24 h-24",
    medium: "w-40 h-40",
    large: "w-60 h-60",
  }[previewSize];

  // Create a unique ID for the input
  const uniqueId = `image-upload-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <div className={`flex flex-col ${className}`}>
      <div className="flex flex-col items-center gap-4">
        {/* Preview Area - Circular */}
        <div className={`relative ${previewSizeClass} mb-1`}>
          <div className="avatar">
            <div
              className={`${previewSizeClass} rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 overflow-hidden`}
            >
              <img
                src={previewUrl || getProfilePlaceholderImage()}
                alt="Preview"
                className="object-cover w-full h-full"
                onError={() => {
                  logger.error("Image preview failed to load");
                  setPreviewUrl("");
                }}
              />
            </div>
          </div>
        </div>

        {/* Upload Button */}
        <label
          htmlFor={uniqueId}
          className={`btn btn-primary  cursor-pointer ${previewSize === "small" ? "btn-sm" : ""}`}
          role="button"
          aria-busy={isLoading}
        >
          {isLoading ? (
            <>
              <span className="loading loading-spinner loading-xs"></span>
              Processing...
            </>
          ) : (
            buttonText
          )}
        </label>
        <input
          id={uniqueId}
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileChange}
          className="hidden"
          disabled={isLoading}
        />
      </div>
    </div>
  );
};
