import { But<PERSON> } from "../But<PERSON>";

const Hero = () => {
  return (
    <section className="w-full min-h-screen flex items-center bg-base-100">
      <div className="container mx-auto px-4 md:px-6 py-12 md:py-24">
        <div className="grid gap-8 lg:grid-cols-2 lg:gap-12 items-center">
          <div className="flex flex-col justify-center space-y-6 animate-fade-in">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl font-display text-base-content">
                <span className="text-primary">Transform</span> School
                Management with E-Bridge
              </h1>
              <p className="text-base-content/70 max-w-[600px] text-lg md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                The all-in-one platform connecting students, teachers, and
                parents for a seamless educational experience.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="w-full sm:w-auto btn-primary" shape="primary">
                Get Started
              </Button>
              <Button
                className="w-full sm:w-auto btn-secondary"
                shape="secondary"
              >
                Learn More
              </Button>
            </div>
          </div>
          <div
            className="w-full h-full animate-fade-in"
            style={{ animationDelay: "0.2s" }}
          >
            <div className="relative h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] w-full overflow-hidden rounded-xl border border-base-300 shadow-lg bg-base-200">
              <img
                src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?auto=format&fit=crop&w=1080&q=80"
                alt="Students using E-Bridge platform"
                className="object-cover w-full h-full rounded-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-xl"></div>
              <div className="absolute bottom-0 left-0 p-6 text-white">
                <p className="text-sm md:text-base font-medium">
                  Empowering education through technology
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
