import type { ButtonHTMLAttributes } from "react";

type ButtonProps = {
  outline?: boolean;
  shape?: "primary" | "neutral" | "secondary" | "accent" | "info";
  pending?: boolean;
  size?: "sm" | "md" | "lg";
  onClick?: () => void;
  children: React.ReactNode;
} & ButtonHTMLAttributes<HTMLButtonElement>;

export const Button = ({
  shape = "primary",
  pending,
  onClick,
  children,
  outline,
  size,
  className,
  ...buttonAttributes
}: ButtonProps) => {
  return (
    <button
      className={`btn left-0.5 ${shape === "primary" ? "btn-primary" : shape === "secondary" ? "btn-secondary" : shape === "accent" ? "btn-accent" : shape === "info" ? "btn-info" : "btn-neutral"} ${outline ? "btn-outline" : ""} ${size === "sm" ? "btn-sm" : size === "lg" ? "btn-lg" : ""}  ${className}`}
      onClick={onClick}
      {...buttonAttributes}
      disabled={pending}
    >
      {pending ? <span className="loading loading-spinner"></span> : children}
    </button>
  );
};
