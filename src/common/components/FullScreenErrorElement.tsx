import { isRouteErrorResponse, useRouteError } from "react-router";
import { AlertTriangle } from "lucide-react";

export default function FullScreenErrorComponent() {
  const error = useRouteError();

  let title = "Something went wrong";
  let message = "An unexpected error occurred.";

  if (isRouteErrorResponse(error)) {
    title = `${error.status} - ${error.statusText}`;
    message = error.data ?? message;
  } else if (error instanceof Error) {
    message = error.message;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 bg-white dark:bg-zinc-900 text-center">
      <div className="text-red-500">
        <AlertTriangle size={48} strokeWidth={1.5} />
      </div>
      <h1 className="mt-4 text-2xl font-bold text-zinc-800 dark:text-zinc-100">
        {title}
      </h1>
      <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400 max-w-md">
        {message}
      </p>
      <a
        href="/"
        className="mt-6 px-4 py-2 bg-red-500 text-white rounded-xl text-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 transition"
      >
        Go Home
      </a>
    </div>
  );
}
