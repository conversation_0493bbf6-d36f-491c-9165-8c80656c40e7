import { z } from "zod";

export const getUuidSchema = (field: string) =>
  z
    .string()
    .min(36, { message: `${field} must be a valid UUID` })
    .max(36, { message: `${field} must be a valid UUID` });

export const positiveNumber = z
  .number({ message: "Weight per bag is required" })
  .positive({ message: "Weight per bag must be a positive number" });

export const emailSchema = z
  .string()
  .email({ message: "Invalid email address" })
  .max(100, { message: "Email must be less than 100 characters" });

export const passwordSchema = z
  .string()
  .min(8, { message: "Password must be at least 8 characters" })
  .regex(
    /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+-_=^~\\[\]{}|:;<>,.()]).+/,
    "Password must include uppercase, lowercase, number and special character",
  );

export const religionSchema = z.enum(
  [
    "ISLAM",
    "CHRISTIANITY",
    "HINDUISM",
    "BUDDHISM",
    "SIKHISM",
    "JUDAISM",
    "OTHER",
  ],
  {
    message: "Religion is required",
  },
);

export const listAllEntitiesQuerySchema = z.object({
  limit: z.coerce
    .number({ message: "Limit should be a number" })
    .max(100, { message: "Limit should be at most 100" })
    .default(100),
  offset: z.coerce
    .number({ message: "Offset should be a number" })
    .max(100, { message: "Offset should be at most 100" })
    .default(0),
});

export const safePositiveNumberSchema = z.coerce
  .number({ message: "Must be a number" })
  .gte(1, { message: "Must be greater than 0" })
  .safe({
    message:
      "Number exceeds maximum supported value (2^53-1). Please enter a smaller number.",
  });

export const nameSchema = z
  .string({ message: "Name is required" })
  .nonempty({ message: "Name is required" })
  .min(2, { message: "Name must be at least 2 characters" })
  .max(70, { message: "Name must be at most 70 characters" })
  .regex(/^[a-zA-Z0-9 .,'&()-]+$/, { message: "Invalid name format" });

export const phoneNumberSchema = z
  .string({
    message: "Phone number must be a string",
  })
  .min(10, { message: "Phone number must be at least 10 digits long" })
  .max(20, { message: "Phone number must be at most 20 digits long" })
  .regex(/^(\+?\d{1,3})?[ -]?\(?\d{2,4}\)?[ -]?\d{3,4}[ -]?\d{3,4}$/, {
    message: "Invalid phone number format",
  });

export const imageUrlSchema = z
  .string()
  .nonempty()
  .min(5, {
    message: "Image url must be at least 5 characters long",
  })
  .max(250, {
    message: "Image url must be at most 250 characters long",
  });

export const dateSchema = z
  .string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format. Use YYYY-MM-DD");

export const dateTimeSchema = z.string().datetime({
  offset: true,
  message: "Invalid date format. Use YYYY-MM-DD HH:MM:SS",
});

export const getForeignResourceSchema = (resource: string) =>
  z.object({
    id: getUuidSchema(`${resource} ID`),
  });

export const addressSchema = z
  .string({ message: "Address is required" })
  .nonempty({ message: "Address is required" })
  .max(255, { message: "Address must be less than 255 characters" });

export const genderSchema = z.enum(["MALE", "FEMALE", "OTHER"], {
  message: "Gender must be one of 'MALE', 'FEMALE', or 'OTHER'.",
});

export const cnicSchema = z
  .string()
  .nonempty()
  .max(15, { message: "CNIC must be less than 15 characters" });
