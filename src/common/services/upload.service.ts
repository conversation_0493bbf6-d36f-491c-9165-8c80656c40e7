import { sendApiRequest } from "./api.service";
import { logger } from "@/lib/logger";

interface UploadResponse {
  url: string;
}

export async function uploadFile(file: File, type: "image" | "pdf") {
  const formData = new FormData();
  formData.append("file", file);
  try {
    return await sendApiRequest<UploadResponse>(`/upload/${type}`, {
      method: "POST",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.error("[UploadService]: Failed to upload file");
    throw error;
  }
}
