import { axiosPrivate, axiosPublic } from "@/api/axios";
import { ApiException } from "@/utils/exception.utils";
import type {
  AxiosError,
  AxiosRequestConfig,
  RawAxiosRequestHeaders,
} from "axios";
import axios from "axios";

export type ApiRequestConfig = {
  method?: AxiosRequestConfig["method"];
  data?: AxiosRequestConfig["data"];
  headers?: RawAxiosRequestHeaders;
  params?: AxiosRequestConfig["params"];
  withCredentials?: AxiosRequestConfig["withCredentials"];
  withAuthorization?: boolean;
};

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}

export async function sendApiRequest<R>(
  url: string,
  options: ApiRequestConfig = {},
) {
  const { withAuthorization, ...axiosOptions } = options;
  const axiosInstance = !withAuthorization ? axiosPublic : axiosPrivate;

  try {
    const response = await axiosInstance<ApiResponse<R>>({
      url,
      ...axiosOptions,
    });
    return response.data.data;
  } catch (error) {
    if (isAxiosError<ApiException>(error) && error.response) {
      const { message, statusCode, code, errors } = error.response.data;
      throw new ApiException(message, statusCode, code, errors);
    } else {
      throw error;
    }
  }
}

function isAxiosError<T>(error: unknown): error is AxiosError<T> {
  return axios.isAxiosError(error);
}
