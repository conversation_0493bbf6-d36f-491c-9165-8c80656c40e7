import { toast } from "react-hot-toast";

export const notify = {
  success: (message = "Operation successful") => {
    cleanupToasts();
    toast.success(message);
  },

  error: (message = "Something went wrong") => {
    cleanupToasts();
    toast.error(message);
  },

  confirmDelete: (callback: () => void) => {
    const id = "confirm-delete";
    toast.dismiss(id); // Prevent duplicate

    return toast(
      (t) => (
        <div className="bg-base-200 text-base-content p-4 rounded-lg shadow-md">
          <p className="font-semibold text-center">
            Are you sure you want to delete?
          </p>
          <div className="flex justify-center gap-3 mt-3">
            <button
              className="btn btn-sm btn-error"
              onClick={() => {
                toast.dismiss(t.id);
                setTimeout(callback, 100);
              }}
            >
              Delete
            </button>
            <button
              className="btn btn-sm btn-secondary"
              onClick={() => {
                toast.dismiss(t.id);
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      ),
      {
        id,
        duration: 8000,
        className: "p-0",
        style: {
          background: "transparent",
          boxShadow: "none",
        },
      },
    );
  },
};

// Optional: Clear all toasts
export const cleanupToasts = () => {
  toast.dismiss();
};
