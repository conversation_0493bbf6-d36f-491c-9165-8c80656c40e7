import { notify } from "@/lib/notify";

interface FieldError {
  message: string;
  field: string;
}

export class ApiException extends Error {
  public constructor(
    public readonly message: string,
    public readonly statusCode: number,
    public readonly code: string,
    public readonly errors?: FieldError[],
  ) {
    super(message);
  }
}

interface ApiExceptionOptions {
  message?: string;
}

const getApiExceptionMessage = (error: ApiException) => {
  switch (error.statusCode) {
    case 401:
      return error.message.includes("password")
        ? error.message
        : "Your session has expired. Please log in again.";
    case 400:
      return error.errors
        ?.map((error) => `${error.field}: ${error.message}`)
        .join("\n");
    default:
      return error.message;
  }
};

export const showErrorNotification = (
  error: unknown,
  options?: ApiExceptionOptions,
) => {
  console.log(error);
  // Handle API exceptions
  if (error instanceof ApiException) {
    const message = options?.message ?? getApiExceptionMessage(error);
    notify.error(message);
    return;
  }

  // Handle other exceptions
  const errorMessage =
    error instanceof Error
      ? error.message
      : (options?.message ?? "Something went wrong. Please try again later.");

  const notifyMessage = options?.message ?? errorMessage;

  notify.error(notifyMessage);
};
