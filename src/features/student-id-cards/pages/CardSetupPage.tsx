import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
  CogIcon,
  PhotoIcon,
  DocumentTextIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";

import { InputField } from "@/common/components/ui/form/InputField";
import { Button } from "@/common/components/ui/Button";
import {
  createCardTemplateSchema,
  type CardSettingsFormData,
} from "../schema/student-id-cards.schema";
import {
  useStudentCardTemplate,
  useCreateStudentCardTemplate,
  useUpdateStudentCardTemplate,
} from "../services/student-id-cards-query";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { uploadFile } from "@/common/services/upload.service";
import { logger } from "@/lib/logger";
import { notify } from "@/lib/notify";

export const CardSetupPage = () => {
  const [headerImageFile, setHeaderImageFile] = useState<File | null>(null);
  const [backgroundImageFile, setBackgroundImageFile] = useState<File | null>(
    null,
  );
  const [headerImagePreview, setHeaderImagePreview] = useState<string | null>(
    null,
  );
  const [backgroundImagePreview, setBackgroundImagePreview] = useState<
    string | null
  >(null);

  const { selectedBranch } = useBranchStore();

  const { data: existingTemplate } = useStudentCardTemplate(selectedBranch?.id);
  const createCardTemplate = useCreateStudentCardTemplate(selectedBranch?.id);
  const updateCardTemplate = useUpdateStudentCardTemplate(selectedBranch?.id);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CardSettingsFormData>({
    resolver: zodResolver(createCardTemplateSchema),
  });

  // Load existing settings
  useEffect(() => {
    if (existingTemplate) {
      setValue("title", existingTemplate.title);
      setValue("watermark", existingTemplate.watermark);
      if (existingTemplate.headerImage) {
        setHeaderImagePreview(existingTemplate.headerImage);
      }
      if (existingTemplate.backgroundImage) {
        setBackgroundImagePreview(existingTemplate.backgroundImage);
      }
    }
  }, [existingTemplate, setValue]);

  // Cleanup object URLs
  useEffect(() => {
    return () => {
      if (headerImagePreview?.startsWith("blob:")) {
        URL.revokeObjectURL(headerImagePreview);
      }
      if (backgroundImagePreview?.startsWith("blob:")) {
        URL.revokeObjectURL(backgroundImagePreview);
      }
    };
  }, [headerImagePreview, backgroundImagePreview]);

  const handleHeaderImageUpload = (file: File) => {
    setHeaderImageFile(file);
    const previewUrl = URL.createObjectURL(file);
    setHeaderImagePreview(previewUrl);
  };

  const handleBackgroundImageUpload = (file: File) => {
    setBackgroundImageFile(file);
    const previewUrl = URL.createObjectURL(file);
    setBackgroundImagePreview(previewUrl);
  };

  const onSubmit: SubmitHandler<CardSettingsFormData> = async (data) => {
    if (!selectedBranch) {
      notify.error("Please select a branch first");
      logger.error("Cannot create card settings. No branch selected");
      return;
    }

    try {
      let headerImageUrl = existingTemplate?.headerImage;
      let backgroundImageUrl = existingTemplate?.backgroundImage;

      // Upload header image if changed
      if (headerImageFile) {
        const headerResponse = await uploadFile(headerImageFile, "image");
        headerImageUrl = headerResponse.url;
      }

      // Upload background image if changed
      if (backgroundImageFile) {
        const backgroundResponse = await uploadFile(
          backgroundImageFile,
          "image",
        );
        backgroundImageUrl = backgroundResponse.url;
      }

      const payload = {
        ...data,
        headerImage: headerImageUrl,
        backgroundImage: backgroundImageUrl,
      };

      if (existingTemplate?.id) {
        await updateCardTemplate.mutateAsync({
          branchId: selectedBranch.id,
          templateId: existingTemplate.id,
          payload,
        });
      } else {
        await createCardTemplate.mutateAsync({
          branchId: selectedBranch.id,
          payload,
        });
      }

      // Show success message or redirect
      notify.success("Card settings saved successfully!");
    } catch (error) {
      console.error("Error saving card settings:", error);
      notify.error("Failed to save card settings. Please try again.");
    }
  };

  const isSubmitting =
    createCardTemplate.isPending || updateCardTemplate.isPending;

  return (
    <div className="min-h-screen bg-base-100 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-6">
          <CogIcon className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold text-base-content">
              ID Card Settings
            </h1>
            <p className="text-base-content/70">
              Configure the design and content of student ID cards
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-base-200 rounded-lg p-6"
          >
            <h2 className="text-xl font-semibold text-base-content mb-4 flex items-center">
              <DocumentTextIcon className="w-5 h-5 mr-2 text-primary" />
              Card Information
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                name="title"
                label="Title"
                placeholder="Enter title"
                register={register}
                errorMessage={errors.title?.message}
              />

              <InputField
                name="watermark"
                label="Watermark"
                placeholder="Enter watermark"
                register={register}
                errorMessage={errors.watermark?.message}
              />
            </div>
          </motion.div>

          {/* Header Image */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-base-200 rounded-lg p-6"
          >
            <h2 className="text-xl font-semibold text-base-content mb-4 flex items-center">
              <PhotoIcon className="w-5 h-5 mr-2 text-primary" />
              Header Image
            </h2>

            <div className="space-y-4">
              <p className="text-sm text-base-content/70">
                Upload a header image for the ID card (recommended: 500x100px)
              </p>

              {headerImagePreview && (
                <div className="border-2 border-dashed border-primary/20 rounded-lg p-4">
                  <img
                    src={headerImagePreview}
                    alt="Header preview"
                    className="max-w-full h-24 object-contain mx-auto rounded"
                  />
                </div>
              )}

              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleHeaderImageUpload(file);
                  }
                }}
                className="file-input file-input-bordered file-input-primary w-full"
              />
            </div>
          </motion.div>

          {/* Background Image */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-base-200 rounded-lg p-6"
          >
            <h2 className="text-xl font-semibold text-base-content mb-4 flex items-center">
              <PhotoIcon className="w-5 h-5 mr-2 text-primary" />
              Background Image
            </h2>

            <div className="space-y-4">
              <p className="text-sm text-base-content/70">
                Upload a background image for the ID card (recommended:
                500x300px)
              </p>

              {backgroundImagePreview && (
                <div className="border-2 border-dashed border-primary/20 rounded-lg p-4">
                  <img
                    src={backgroundImagePreview}
                    alt="Background preview"
                    className="max-w-full h-40 object-contain mx-auto rounded"
                  />
                </div>
              )}

              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleBackgroundImageUpload(file);
                  }
                }}
                className="file-input file-input-bordered file-input-primary w-full"
              />
            </div>
          </motion.div>

          {/* Card Preview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-base-200 rounded-lg p-6"
          >
            <h2 className="text-xl font-semibold text-base-content mb-4">
              Preview
            </h2>

            <div className="flex justify-center">
              <div className="w-80 h-[470px] bg-white rounded-lg shadow-lg flex flex-col justify-center overflow-hidden relative border">
                {/* Header */}
                {headerImagePreview ? (
                  <img
                    src={headerImagePreview}
                    alt="Header"
                    className="w-full top-0 absolute z-20 h-20 object-cover"
                  />
                ) : (
                  <div className="w-full h-20 absolute top-0 bg-primary/10 flex items-center justify-center">
                    <span className="text-sm text-primary font-medium">
                      {watch("title") ?? "Institution Name"}
                    </span>
                  </div>
                )}

                {/* Background */}
                {backgroundImagePreview && (
                  <img
                    src={backgroundImagePreview}
                    alt="Background"
                    className="absolute inset-0 w-full h-full object-cover opacity-90"
                  />
                )}

                {/* Content */}
                <div className="relative p-4 text-center">
                  <div>
                    <div className="w-16 h-16 overflow-hidden bg-gray-300 rounded-full mx-auto mb-2">
                      <img
                        src="/profilePlaceHolder.jpg"
                        alt="Student"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="font-semibold text-sm">Student Name</h3>
                    <p className="text-xs text-gray-600">Class: 10th - A</p>
                    <p className="text-xs text-gray-600">Roll no: 123</p>
                  </div>
                  <div className="w-[150px] h-[150px] mt-10 bg-gray-200 mx-auto p-1 rounded">
                    <img
                      src="/card_template_qr.png"
                      alt="Student"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="absolute min-h-6 bottom-0 w-full bg-gray-800 text-white text-xs text-center py-1">
                  {watch("watermark")}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 min-w-32"
            >
              {isSubmitting ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <CheckIcon className="w-4 h-4" />
                  <span>Save Settings</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
