import type { StudentEnrollment } from "@/features/students/types/students.type";
import { z } from "zod";
import type { createCardTemplateSchema } from "../schema/student-id-cards.schema";

export interface StudentCard {
  id: string;
  title?: string;
  watermark?: string;
  headerImage?: string;
  backgroundImage: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SelectedStudent {
  enrollmentId: string;
  studentId: string;
  name: string;
  rollNumber: number;
  className: string;
  sectionName: string;
  studentImage?: string;
}

export interface CardGenerationData {
  student: SelectedStudent;
  settings: StudentCard;
  qrCodeData: string;
}

export interface StudentIdCardFilters {
  classId?: string;
  sectionId?: string;
  status?: StudentEnrollment["status"];
}

export interface UpdateEnrollmentCardPayload {
  card: string; // URL or identifier for the generated card
}

export interface CardDownloadOptions {
  format: "png" | "jpeg";
  quality?: number; // For JPEG format (0-1)
}

export interface BulkCardGenerationRequest {
  enrollmentIds: string[];
  settings: StudentCard;
  downloadOptions: CardDownloadOptions;
}

export interface CardGenerationResponse {
  success: boolean;
  cardUrl?: string;
  error?: string;
}

export type CreateCardTemplatePayload = z.infer<
  typeof createCardTemplateSchema
>;

export type UpdateCardTemplatePayload = Partial<CreateCardTemplatePayload>;
