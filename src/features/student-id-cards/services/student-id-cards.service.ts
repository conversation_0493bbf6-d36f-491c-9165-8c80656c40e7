import { sendApiRequest } from "@/common/services/api.service";
import { logger } from "@/lib/logger";
import type {
  StudentCard,
  CreateCardTemplatePayload,
  UpdateCardTemplatePayload,
  UpdateEnrollmentCardPayload,
} from "../types/student-id-cards.type";

// Card Settings API functions
export async function fetchStudentCardTemplate(
  branchId: string,
): Promise<StudentCard | null> {
  try {
    return await sendApiRequest<StudentCard>(
      `/branches/${branchId}/student-card-templates`,
      {
        method: "GET",
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching card settings", error);
    throw error;
  }
}

interface CreateCardSettingsParams {
  branchId: string;
  payload: CreateCardTemplatePayload;
}
export async function createCardSettings({
  branchId,
  payload,
}: CreateCardSettingsParams): Promise<StudentCard> {
  try {
    return await sendApiRequest<StudentCard>(
      `/branches/${branchId}/student-card-templates`,
      {
        method: "POST",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error creating card settings", error);
    throw error;
  }
}

interface UpdateCardSettingsParams {
  branchId: string;
  templateId: string;
  payload: UpdateCardTemplatePayload;
}
export async function updateStudentCardTemplate({
  branchId,
  templateId,
  payload,
}: UpdateCardSettingsParams): Promise<StudentCard> {
  try {
    return await sendApiRequest<StudentCard>(
      `/branches/${branchId}/student-card-templates/${templateId}`,
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating card settings", error);
    throw error;
  }
}

// Enrollment Card Update API function
export async function updateEnrollmentCard(
  enrollmentId: string,
  payload: UpdateEnrollmentCardPayload,
): Promise<void> {
  try {
    await sendApiRequest(`/enrollments/${enrollmentId}`, {
      method: "PATCH",
      withAuthorization: true,
      data: payload,
    });
  } catch (error: unknown) {
    logger.error("Error updating enrollment card", error);
    throw error;
  }
}

// Bulk update enrollments with card information
export async function bulkUpdateEnrollmentCards(
  updates: Array<{ enrollmentId: string; card: string }>,
): Promise<void> {
  try {
    await sendApiRequest("/enrollments/bulk-update-cards", {
      method: "PATCH",
      withAuthorization: true,
      data: { updates },
    });
  } catch (error: unknown) {
    logger.error("Error bulk updating enrollment cards", error);
    throw error;
  }
}

// Image upload functions
export async function uploadCardImage(
  file: File,
  type: "header" | "background",
): Promise<{ url: string }> {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);

    return await sendApiRequest<{ url: string }>("/upload/card-images", {
      method: "POST",
      withAuthorization: true,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  } catch (error: unknown) {
    logger.error("Error uploading card image", error);
    throw error;
  }
}
