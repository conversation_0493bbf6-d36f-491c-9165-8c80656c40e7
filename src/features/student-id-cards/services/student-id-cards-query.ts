import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  fetchStudentCardTemplate,
  createCardSettings,
  updateStudentCardTemplate,
  updateEnrollmentCard,
  bulkUpdateEnrollmentCards,
  uploadCardImage,
} from "./student-id-cards.service";
import type { UpdateEnrollmentCardPayload } from "../types/student-id-cards.type";

const getStudentCardTemplateQueryKey = (branchId?: string) => [
  "student-card-template",
  branchId,
];

// Card Settings Queries
export const useStudentCardTemplate = (branchId?: string) => {
  return useQuery({
    queryKey: getStudentCardTemplateQueryKey(branchId),
    queryFn: () => {
      if (branchId) {
        return fetchStudentCardTemplate(branchId);
      }
    },
    enabled: Boolean(branchId),
  });
};

// Card Settings Mutations
export const useCreateStudentCardTemplate = (branchId?: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCardSettings,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getStudentCardTemplateQueryKey(branchId),
      });
    },
  });
};

export const useUpdateStudentCardTemplate = (branchId?: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateStudentCardTemplate,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getStudentCardTemplateQueryKey(branchId),
      });
    },
  });
};

// Enrollment Card Mutations
export const useUpdateEnrollmentCard = () => {
  return useMutation({
    mutationFn: ({
      enrollmentId,
      payload,
    }: {
      enrollmentId: string;
      payload: UpdateEnrollmentCardPayload;
    }) => updateEnrollmentCard(enrollmentId, payload),
  });
};

export const useBulkUpdateEnrollmentCards = () => {
  return useMutation({
    mutationFn: bulkUpdateEnrollmentCards,
  });
};

// Image Upload Mutations
export const useUploadCardImage = () => {
  return useMutation({
    mutationFn: ({
      file,
      type,
    }: {
      file: File;
      type: "header" | "background";
    }) => uploadCardImage(file, type),
  });
};
