import { motion } from "framer-motion";
import type { StudentCard } from "../types/student-id-cards.type";

export interface StudentCardProps
  extends Omit<StudentCard, "id" | "createdAt"> {
  qrCode: string;
  studentName: string;
  studentClass: string;
  studentSection: string;
  studentRollNumber: number;
  studentPhoto?: string;
  setRef?: (element: HTMLDivElement | null) => void;
}

export const StudentCardPreview = (props: StudentCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-base-200 rounded-lg p-6"
    >
      <h2 className="text-xl font-semibold text-base-content mb-4">Preview</h2>

      <div className="flex justify-center">
        <div
          className="w-80 h-[470px] bg-white rounded-lg shadow-lg flex flex-col justify-center overflow-hidden relative border"
          ref={(el) => {
            if (props.setRef) props.setRef(el);
          }}
        >
          {/* Header */}
          {props.headerImage ? (
            <img
              src={props.headerImage}
              alt="Header"
              className="w-full top-0 absolute z-20 h-20 object-cover"
            />
          ) : (
            <div className="w-full h-20 absolute top-0 bg-primary/10 flex items-center justify-center">
              <span className="text-sm text-primary font-medium">
                {props.title}
              </span>
            </div>
          )}

          {/* Background */}
          {props.backgroundImage && (
            <img
              src={props.backgroundImage}
              alt="Background"
              className="absolute inset-0 w-full h-full object-cover opacity-90"
            />
          )}

          {/* Content */}
          <div className="relative p-4 text-center">
            <div>
              <div className="w-16 h-16 overflow-hidden bg-gray-300 rounded-full mx-auto mb-2">
                <img
                  src={props.studentPhoto ?? "/profilePlaceHolder.jpg"}
                  alt="Student"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="font-semibold text-sm">{props.studentName}</h3>
              <p className="text-xs text-gray-600">
                Class: {`${props.studentClass} - ${props.studentSection}`}
              </p>
              <p className="text-xs text-gray-600">
                Roll no: {props.studentRollNumber}
              </p>
            </div>
            <div className="w-[150px] h-[150px] bg-gray-200 mx-auto p-1 rounded mt-2">
              <img
                src={props.qrCode}
                alt="Student"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Footer */}
          <div className="absolute min-h-6 bottom-0 w-full bg-gray-800 text-white text-xs text-center py-1">
            {props.watermark}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
