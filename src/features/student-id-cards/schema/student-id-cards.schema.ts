import { z } from "zod";

export const createCardTemplateSchema = z.object({
  title: z
    .string()
    .min(1, "Institution name is required")
    .max(100, "Institution name must be less than 100 characters")
    .optional(),
  watermark: z
    .string()
    .min(1, "Branch name is required")
    .max(100, "Branch name must be less than 100 characters")
    .optional(),
  headerImage: z.string().url("Invalid header image URL").optional(),
  backgroundImage: z.string().url("Invalid background image URL").optional(),
});

export const studentSelectionSchema = z.object({
  enrollmentIds: z
    .array(z.string())
    .min(1, "At least one student must be selected"),
});

export const cardDownloadOptionsSchema = z.object({
  format: z.enum(["png", "jpeg"], {
    required_error: "Download format is required",
  }),
  quality: z.number().min(0.1).max(1).optional().default(0.9),
});

export const bulkCardGenerationSchema = z.object({
  enrollmentIds: z
    .array(z.string())
    .min(1, "At least one student must be selected"),
  settings: createCardTemplateSchema,
  downloadOptions: cardDownloadOptionsSchema,
});

export const studentIdCardFiltersSchema = z.object({
  classId: z.string().optional(),
  sectionId: z.string().optional(),
  status: z
    .enum([
      "ACTIVE",
      "EXPELLED",
      "GRADUATED",
      "DECEASED",
      "COMPLETED",
      "WITHDRAWN",
    ])
    .optional(),
});

export const updateEnrollmentCardSchema = z.object({
  card: z.string().min(1, "Card identifier is required"),
});

export type CardSettingsFormData = z.infer<typeof createCardTemplateSchema>;
export type StudentSelectionFormData = z.infer<typeof studentSelectionSchema>;
export type CardDownloadOptionsFormData = z.infer<
  typeof cardDownloadOptionsSchema
>;
export type BulkCardGenerationFormData = z.infer<
  typeof bulkCardGenerationSchema
>;
export type StudentIdCardFiltersFormData = z.infer<
  typeof studentIdCardFiltersSchema
>;
export type UpdateEnrollmentCardFormData = z.infer<
  typeof updateEnrollmentCardSchema
>;
