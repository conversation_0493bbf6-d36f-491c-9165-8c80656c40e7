import {
  addressSchema,
  cnicSchema,
  dateSchema,
  genderSchema,
  imageUrlSchema,
  nameSchema,
  passwordSchema,
  phoneNumberSchema,
  religionSchema,
} from "@/common/schemas/zod-common.schemas";
import { z } from "zod";
const studentAdmissionBaseSchema = z.object({
  name: nameSchema,
  fatherName: nameSchema,
  religion: religionSchema,
  registrationNumber: z
    .string()
    .nonempty()
    .max(100, {
      message: "Registration number must be less than 100 characters",
    })
    .optional(),
  email: z
    .string()
    .max(100, { message: "Email must be less than 100 characters" })
    .email({ message: "Invalid email address" })
    .or(z.literal(""))
    .optional(),
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.optional(),
  monthlyFee: z.number().positive().max(1_000_000, {
    message: "Monthly fee cannot exceed 1,000,000",
  }),
  admissionDate: dateSchema,
  dateOfBirth: dateSchema,
  previousSchool: z
    .string()
    .max(100, {
      message: "Previous school name must be less than 100 characters",
    })
    .optional(),
  guardianName: nameSchema,
  guardianPhone: phoneNumberSchema,
  guardianEmail: z.string().email().max(100, {
    message: "Email must be less than 100 characters",
  }),
  guardianPassword: passwordSchema,
  guardianAddress: addressSchema,
  guardianGender: genderSchema,
  guardianRelation: z.enum(["FATHER", "MOTHER", "GUARDIAN"], {
    message:
      "Guardian relation must be one of 'FATHER', 'MOTHER', or 'GUARDIAN'.",
  }),
  guardianCnic: cnicSchema,
  classSectionId: z
    .string({ message: "Class section is required" })
    .nonempty({ message: "Class section is required" }),
});

export const createStudentAdmissionSchema = studentAdmissionBaseSchema;

export const updateStudentAdmissionSchema =
  studentAdmissionBaseSchema.partial();
