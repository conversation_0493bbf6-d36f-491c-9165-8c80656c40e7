import { sendApiRequest } from "@/common/services/api.service";
import type {
  CreateStudentAdmissionPayload,
  FetchStudentQueryParams,
  StudentEnrollment,
} from "../types/students.type";
import { logger } from "@/lib/logger";
import type { PaginatedApiResponse } from "@/common/types/global.types";

type CreateStudentAdmissionParams = {
  academicSessionId: string;
  payload: CreateStudentAdmissionPayload;
};

export async function createStudentAdmission({
  academicSessionId,
  payload,
}: CreateStudentAdmissionParams): Promise<void> {
  try {
    await sendApiRequest(`/academic-sessions/${academicSessionId}/students`, {
      method: "POST",
      withAuthorization: true,
      data: payload,
    });
  } catch (error: unknown) {
    logger.error("Error creating student admission", error);
    throw error;
  }
}

export async function fetchAllStudentEnrollments(
  academicSessionId: string,
  params?: FetchStudentQueryParams,
) {
  try {
    return await sendApiRequest<PaginatedApiResponse<StudentEnrollment>>(
      `/academic-sessions/${academicSessionId}/enrollments`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching students", error);
    throw error;
  }
}
