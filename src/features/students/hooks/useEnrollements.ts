import { useQuery } from "@tanstack/react-query";
import { fetchAllStudentEnrollments } from "../service/students.service";
import type { FetchStudentQueryParams } from "../types/students.type";

export const getEnrollmentsQueryKey = (
  academicSessionId?: string,
  params?: FetchStudentQueryParams,
) =>
  params
    ? [
        "students",
        academicSessionId,
        params.classId,
        params.status,
        params.sectionId,
        params.offset,
        params.limit,
      ]
    : ["students", academicSessionId];

export const useEnrollments = (
  academicSessionId?: string,
  params?: FetchStudentQueryParams,
) => {
  return useQuery({
    queryKey: getEnrollmentsQueryKey(academicSessionId, params),
    queryFn: () => {
      if (academicSessionId) {
        return fetchAllStudentEnrollments(academicSessionId, params);
      }
    },
    enabled: <PERSON><PERSON><PERSON>(academicSessionId),
  });
};
