import { useMutation } from "@tanstack/react-query";
import { createStudentAdmission } from "../service/students.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { logger } from "@/lib/logger";

export function useCreateStudentAdmission(academicSessionId?: string) {
  logger.log(academicSessionId);
  return useMutation({
    mutationFn: createStudentAdmission,
    onError: (error) => {
      showErrorNotification(error);
    },
    onSuccess: () => {
      notifyResourceActionSuccess("Student", "create");
    },
  });
}
