import { useState } from "react";
import { motion } from "framer-motion";
import { UserGroupIcon } from "@heroicons/react/24/outline";

import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { useEnrollments } from "../hooks/useEnrollements";
import { useClasses } from "@/features/classes/service/class-query";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { SelectField } from "@/common/components/ui/form/SelectField";
import type {
  StudentEnrollment,
  FetchStudentQueryParams,
} from "../types/students.type";
import type { PaginationParams } from "@/common/types/global.types";
import { apiParams } from "@/common/constants/api-params.constant";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { formatDateInLocalFormat } from "@/utils/utils";

export const StudentsListPage = () => {
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  const [filters, setFilters] = useState<FetchStudentQueryParams>({
    ...paginationParams,
  });

  const [selectedStudent, setSelectedStudent] =
    useState<StudentEnrollment | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);
  const { data: enrollmentsData, isLoading } = useEnrollments(
    activeAcademicSession?.id,
    { ...paginationParams, ...filters },
  );

  const handleClassChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const classId = e.target.value;
    setFilters((prev) => ({
      ...prev,
      classId,
      sectionId: undefined, // Reset section when class changes
    }));
  };

  const handleSectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sectionId = e.target.value;
    setFilters((prev) => ({ ...prev, sectionId }));
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const enrollmentStatus = e.target.value as
      | StudentEnrollment["status"]
      | undefined;
    setFilters((prev) => ({ ...prev, status: enrollmentStatus }));
  };

  const getClassOptions = () => {
    return [
      ...(classesData?.items.map((cls) => ({
        value: cls.id,
        label: cls.name,
      })) ?? []),
    ];
  };

  const getSectionOptions = () => {
    if (!filters.classId) return [];

    const selectedClass = classesData?.items.find(
      (cls) => cls.id === filters.classId,
    );
    return [
      ...(selectedClass?.sections.map((section) => ({
        value: section.id,
        label: section.name,
      })) ?? []),
    ];
  };

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "ACTIVE", label: "Active" },
    { value: "EXPELLED", label: "Expelled" },
    { value: "GRADUATED", label: "Graduated" },
    { value: "DECEASED", label: "Deceased" },
    { value: "COMPLETED", label: "Completed" },
    { value: "WITHDRAWN", label: "Withdrawn" },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <UserGroupIcon className="w-8 h-8 text-primary" />
        <div>
          <h1 className="text-2xl font-bold text-base-content">Students</h1>
          <p className="text-base-content/70">Manage all enrolled students</p>
        </div>
      </div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-base-200 rounded-lg p-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SelectField
            name="classId"
            label="Filter by Class"
            value={filters.classId}
            options={getClassOptions()}
            onChange={handleClassChange}
          />

          <SelectField
            name="sectionId"
            label="Filter by Section"
            value={filters.sectionId}
            options={getSectionOptions()}
            onChange={handleSectionChange}
            disabled={!filters.classId}
          />

          <SelectField
            name="status"
            label="Filter by Status"
            value={filters.status}
            options={statusOptions}
            onChange={handleStatusChange}
          />
        </div>
      </motion.div>

      {/* Students Table */}
      <ResourceListingTable
        records={enrollmentsData?.items ?? []}
        totalRecords={enrollmentsData?.total ?? 0}
        isDataLoading={isLoading}
        searchLabel="student name"
        searchFieldValue={(record) => record.student.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Name", render: (s) => s.student.name },
          { label: "Roll No", render: (s) => s.student.rollNumber },
          { label: "Class", render: (s) => s.classSection.class.name },
          { label: "Section", render: (s) => s.classSection.name },
          {
            label: "Status",
            render: (s) => (
              <span
                className={`badge badge-sm ${
                  s.status === "ACTIVE" ? "badge-success" : "badge-warning"
                }`}
              >
                {s.status}
              </span>
            ),
          },
          {
            label: "Enrollment Type",
            render: (s) => s.type.replace("_", " "),
          },
        ]}
        onView={(item) => {
          setSelectedStudent(item);
          setIsViewModalOpen(true);
        }}
      />

      {/* View Student Modal */}
      {isViewModalOpen && selectedStudent && (
        <ViewRecordModal
          title="Student Details"
          subtitle={selectedStudent.student.name}
          record={selectedStudent}
          columns={[
            { label: "Student Name", render: (s) => s.student.name },
            { label: "Roll Number", render: (s) => s.student.rollNumber },
            { label: "Class", render: (s) => s.classSection.class.name },
            { label: "Section", render: (s) => s.classSection.name },
            {
              label: "Class Teacher",
              render: (s) => s.classSection.classTeacher.name,
            },
            { label: "Status", render: (s) => s.status },
            {
              label: "Enrollment Type",
              render: (s) => s.type.replace("_", " "),
            },
            { label: "Academic Session", render: (s) => s.academicSessionName },
            {
              label: "Enrollment Date",
              render: (s) => formatDateInLocalFormat(s.date),
            },
            {
              label: "Created At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
            {
              label: "ID Card Status",
              render: (s) => (s.card ? "Generated" : "Not Generated"),
            },
          ]}
          onUpdate={() => {
            // For now, just close the modal
            setIsViewModalOpen(false);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedStudent(null);
          }}
        />
      )}
    </div>
  );
};
