import { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  UserIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";

import { InputField } from "@/common/components/ui/form/InputField";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { Button } from "@/common/components/ui/Button";
import { createStudentAdmissionSchema } from "../schema/students.schema";
import { useClasses } from "@/features/classes/service/class-query";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { UploadImage } from "@/common/components/ui/UploadImge";
import { getProfilePlaceholderImage } from "@/utils/image.utils";
import { removeEmptyFieldsFromObject } from "@/utils/utils";
import { uploadFile } from "@/common/services/upload.service";
import { logger } from "@/lib/logger";
import type { CreateStudentAdmissionPayload } from "../types/students.type";
import { useCreateStudentAdmission } from "../hooks/useCreateStudentHook";
import { notify } from "@/lib/notify";

export const StudentAdmissionPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedClassId, setSelectedClassId] = useState<string>("");
  const [studentPhotoFile, setStudentPhotoFile] = useState<File | null>(null);

  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);

  // Mutations for create and update
  const createStudentAdmissionMutation = useCreateStudentAdmission(
    activeAcademicSession?.id,
  );

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
    reset: resetForm,
    setValue,
  } = useForm<CreateStudentAdmissionPayload>({
    resolver: zodResolver(createStudentAdmissionSchema),
    mode: "onChange",
  });

  const handleNext = async () => {
    const fieldsToValidate = [
      "name",
      "fatherName",
      "email",
      "address",
      "gender",
      "monthlyFee",
      "admissionDate",
      "dateOfBirth",
      "religion",
      "classSectionId",
    ] as const;

    const isStepValid = await trigger(fieldsToValidate);
    if (isStepValid) {
      setCurrentStep(2);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(1);
  };

  const uploadStudentPhoto = async (file: File) => {
    try {
      const { url } = await uploadFile(file, "image");
      return url;
    } catch (error) {
      logger.error("Error uploading student photo:", error);
      notify.error("Failed to upload student photo");
      throw error;
    }
  };

  const onSubmit: SubmitHandler<CreateStudentAdmissionPayload> = async (
    data,
  ) => {
    if (!activeAcademicSession) {
      logger.error("Cannot create student. No active academic session");
      return;
    }

    try {
      if (studentPhotoFile) {
        const studentPhotoUrl = await uploadStudentPhoto(studentPhotoFile);
        data.photo = studentPhotoUrl;
      }

      await createStudentAdmissionMutation.mutateAsync({
        academicSessionId: activeAcademicSession.id,
        payload: removeEmptyFieldsFromObject(data),
      });

      resetForm({});
      setStudentPhotoFile(null);
      setCurrentStep(1);
    } catch (error) {
      logger.error("Error creating student admission", error);
    }
  };

  const handleClassChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const classId = e.target.value;
    setSelectedClassId(classId);
    // Reset section selection when class changes
    setValue("classSectionId", "");
  };

  const getClassOptions = () => {
    return (
      classesData?.items.map((cls) => ({
        value: cls.id,
        label: cls.name,
      })) ?? []
    );
  };

  const getSectionOptions = () => {
    if (!selectedClassId) return [];

    const selectedClass = classesData?.items.find(
      (cls) => cls.id === selectedClassId,
    );
    return (
      selectedClass?.sections.map((section) => ({
        value: section.id,
        label: section.name,
      })) ?? []
    );
  };

  const genderOptions = [
    { value: "MALE", label: "Male" },
    { value: "FEMALE", label: "Female" },
    { value: "OTHER", label: "Other" },
  ];

  const religionOptions = [
    { value: "ISLAM", label: "Islam" },
    { value: "CHRISTIANITY", label: "Christianity" },
    { value: "HINDUISM", label: "Hinduism" },
    { value: "OTHER", label: "Other" },
  ];

  const guardianRelationOptions = [
    { value: "FATHER", label: "Father" },
    { value: "MOTHER", label: "Mother" },
    { value: "GUARDIAN", label: "Guardian" },
  ];

  return (
    <div className="min-h-screen bg-base-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-base-content mb-2">
            Student Admission Form
          </h1>
          <p className="text-base-content/70">
            Complete the form to admit a new student
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div
              className={`flex items-center space-x-2 ${currentStep >= 1 ? "text-primary" : "text-base-content/50"}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? "bg-primary text-white" : "bg-base-300"}`}
              >
                <UserIcon className="w-4 h-4" />
              </div>
              <span className="font-medium">Student Info</span>
            </div>

            <div className="w-12 h-0.5 bg-base-300"></div>
            <div
              className={`flex items-center space-x-2 ${currentStep >= 2 ? "text-primary" : "text-base-content/50"}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? "bg-primary text-white" : "bg-base-300"}`}
              >
                <UsersIcon className="w-4 h-4" />
              </div>
              <span className="font-medium">Guardian Info</span>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Step 1: Student Information */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.3 }}
              className="bg-base-200 rounded-lg p-6 shadow-lg"
            >
              <h2 className="text-xl font-semibold text-base-content mb-6 flex items-center">
                <UserIcon className="w-5 h-5 mr-2 text-primary" />
                Student Information
              </h2>

              <div className="mb-6">
                <UploadImage
                  initialPreview={getProfilePlaceholderImage()}
                  onImageSelected={setStudentPhotoFile}
                  buttonText="Upload Photo"
                  previewSize="small"
                />
                <p className="text-sm text-base-content/70 text-center py-2">
                  Add student's photo (Optional)
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  name="name"
                  label="Student Name"
                  placeholder="Enter student's full name"
                  register={register}
                  errorMessage={errors.name?.message}
                />

                <InputField
                  name="fatherName"
                  label="Father's Name"
                  placeholder="Enter father's name"
                  register={register}
                  errorMessage={errors.fatherName?.message}
                />

                <InputField
                  name="email"
                  label="Email Address(Optional)"
                  type="email"
                  placeholder="Enter email address"
                  register={register}
                  errorMessage={errors.email?.message}
                />

                <SelectField
                  name="gender"
                  label="Gender"
                  register={register}
                  options={genderOptions}
                  errorMessage={errors.gender?.message}
                />

                <InputField
                  name="dateOfBirth"
                  label="Date of Birth"
                  type="date"
                  register={register}
                  errorMessage={errors.dateOfBirth?.message}
                />

                <SelectField
                  name="religion"
                  label="Religion"
                  register={register}
                  options={religionOptions}
                  errorMessage={errors.religion?.message}
                />

                <InputField
                  name="address"
                  label="Address"
                  placeholder="Enter complete address"
                  register={register}
                  errorMessage={errors.address?.message}
                />

                <InputField
                  name="monthlyFee"
                  label="Monthly Fee"
                  type="number"
                  placeholder="Enter monthly fee"
                  register={register}
                  valueAsNumber
                  errorMessage={errors.monthlyFee?.message}
                />

                <InputField
                  name="admissionDate"
                  label="Admission Date"
                  type="date"
                  register={register}
                  errorMessage={errors.admissionDate?.message}
                />

                <InputField
                  name="previousSchool"
                  label="Previous School (Optional)"
                  placeholder="Enter previous school name"
                  register={register}
                  errorMessage={errors.previousSchool?.message}
                />

                <SelectField
                  name="classId"
                  label="Class"
                  value={selectedClassId}
                  options={getClassOptions()}
                  onChange={handleClassChange}
                />

                <SelectField
                  name="classSectionId"
                  label="Section"
                  register={register}
                  options={getSectionOptions()}
                  errorMessage={errors.classSectionId?.message}
                  disabled={!selectedClassId}
                />
              </div>

              <div className="flex justify-end mt-8">
                <Button
                  type="button"
                  onClick={handleNext}
                  className="flex items-center space-x-2"
                >
                  <span>Next</span>
                  <ChevronRightIcon className="w-4 h-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 2: Guardian Information */}
          {currentStep === 2 && (
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.3 }}
              className="bg-base-200 rounded-lg p-6 shadow-lg"
            >
              <h2 className="text-xl font-semibold text-base-content mb-6 flex items-center">
                <UsersIcon className="w-5 h-5 mr-2 text-primary" />
                Guardian Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  name="guardianName"
                  label="Guardian Name"
                  placeholder="Enter guardian's full name"
                  register={register}
                  errorMessage={errors.guardianName?.message}
                />

                <SelectField
                  name="guardianRelation"
                  label="Relation to Student"
                  register={register}
                  options={guardianRelationOptions}
                  errorMessage={errors.guardianRelation?.message}
                />

                <InputField
                  name="guardianPhone"
                  label="Phone Number"
                  placeholder="Enter phone number"
                  register={register}
                  errorMessage={errors.guardianPhone?.message}
                />

                <SelectField
                  name="guardianGender"
                  label="Gender"
                  register={register}
                  options={genderOptions}
                  errorMessage={errors.guardianGender?.message}
                />

                <InputField
                  name="guardianCnic"
                  label="CNIC"
                  placeholder="Enter CNIC (e.g., 12345-6789012-3)"
                  register={register}
                  errorMessage={errors.guardianCnic?.message}
                />

                <InputField
                  name="guardianAddress"
                  label="Address"
                  placeholder="Enter complete address"
                  register={register}
                  errorMessage={errors.guardianAddress?.message}
                />

                <InputField
                  name="guardianEmail"
                  label="Email Address"
                  type="email"
                  placeholder="Enter email address"
                  register={register}
                  errorMessage={errors.guardianEmail?.message}
                />

                <InputField
                  name="guardianPassword"
                  label="Password"
                  type="password"
                  placeholder="Enter password for guardian account"
                  register={register}
                  errorMessage={errors.guardianPassword?.message}
                />
              </div>

              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  onClick={handlePrevious}
                  shape="neutral"
                  outline
                  className="flex items-center space-x-2"
                >
                  <ChevronLeftIcon className="w-4 h-4" />
                  <span>Previous</span>
                </Button>

                <Button type="submit" className="flex items-center space-x-2">
                  <span>Submit Application</span>
                </Button>
              </div>
            </motion.div>
          )}
        </form>
      </div>
    </div>
  );
};
