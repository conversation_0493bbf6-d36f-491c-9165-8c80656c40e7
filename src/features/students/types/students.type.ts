import type { PaginationParams } from "@/common/types/global.types";
import type { createStudentAdmissionSchema } from "../schema/students.schema";

import { z } from "zod";

export type CreateStudentAdmissionPayload = z.infer<
  typeof createStudentAdmissionSchema
>;

export interface FetchStudentQueryParams extends PaginationParams {
  classId?: string;
  sectionId?: string;
  status?: StudentEnrollment["status"];
}

export interface StudentEnrollment {
  id: string;
  type: "ADMISSION" | "TRANSFER_IN" | "REPEATING" | "PROMOTION";
  card?: string;
  status:
    | "ACTIVE"
    | "EXPELLED"
    | "GRADUATED"
    | "DECEASED"
    | "COMPLETED"
    | "WITHDRAWN";
  date: string;
  student: {
    id: string;
    name: string;
    photo?: string;
    rollNumber: number;
  };
  classSection: {
    id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    totalStudents: number;
    class: {
      id: string;
      name: string;
    };
    classTeacher: {
      id: string;
      name: string;
    };
  };
  academicSessionName: string;
  createdAt: string;
}
