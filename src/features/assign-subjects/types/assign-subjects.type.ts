import { z } from "zod";
import type { createSubjectAssignmentSchema } from "../schema/assignSubjects.schema";

export interface SubjectAssignment {
  id: string;
  academicSessionId: string;
  class: {
    id: string;
    name: string;
  };
  subject: {
    id: string;
    name: string;
  };
  section: {
    id: string;
    name: string;
  };
  subjectTeacher: {
    id: string;
    name: string;
  };
  createdAt: string;
}

export type CreateSubjectAssignmentPayload = z.infer<
  typeof createSubjectAssignmentSchema
>;
