import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import { ResourcePage } from "@/common/components/ResourcePage";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { zodResolver } from "@hookform/resolvers/zod";
import { type SubmitHandler, useForm } from "react-hook-form";
import { createSubjectAssignmentSchema } from "../schema/assignSubjects.schema";
import { z } from "zod";
import { useClasses } from "../../classes/service/class-query";
import { useAcademicSessionStore } from "../../academic-session/session.store";
import { type ChangeEvent, useEffect, useState } from "react";
import type { Class } from "../../classes/types/class.type";
import { useSubjects } from "../../subjects/service/subjects-query";
import { useStaffByType } from "../../staff/services/staff-query";
import { useBranchStore } from "../../onboarding/setup/branches/branches.store";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import {
  useAssignedSubjects,
  useCreateAssignedSubject,
  useUpdateAssignedSubject,
} from "../service/assign-subjects-query";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import type { SubjectAssignment } from "../types/assign-subjects.type";
import { logger } from "@/lib/logger";
import { formatDateInLocalFormat } from "@/utils/utils";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";

export const AssignSubjectsPage = () => {
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedAssignedSubject, setSelectedAssignedSubject] =
    useState<SubjectAssignment | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  // Fetch data
  const { selectedBranch } = useBranchStore();
  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData } = useClasses(activeAcademicSession?.id);
  const { data: subjectsData } = useSubjects(activeAcademicSession?.id);
  const { data: teachersData } = useStaffByType("TEACHER", selectedBranch?.id);
  const { data: assignedSubjectsData } = useAssignedSubjects(
    activeAcademicSession?.id,
    paginationParams,
  );

  // Mutations for create and update
  const createAssignedSubjectMutation = useCreateAssignedSubject(
    activeAcademicSession?.id,
  );
  const updateAssignedSubjectMutation = useUpdateAssignedSubject(
    activeAcademicSession?.id,
  );

  // Form setup
  type FormData = z.infer<typeof createSubjectAssignmentSchema>;

  const {
    register,
    handleSubmit,
    reset: resetForm,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(createSubjectAssignmentSchema),
  });

  const handleClassChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const selectedClassId = e.target.value;
    const selectedClass = classesData?.items.find(
      (c) => c.id === selectedClassId,
    );
    setSelectedClass(selectedClass ?? null);
  };

  const handleEdit = (assignedSubject: SubjectAssignment) => {
    setSelectedAssignedSubject(assignedSubject);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedClass(null);
    resetForm({
      subjectId: "",
      subjectTeacherId: "",
      classSectionId: "",
    });
  };

  const onSubmit: SubmitHandler<FormData> = (data) => {
    if (!activeAcademicSession) {
      logger.error(
        "Cannot create subject assignment. No active academic session",
      );
      return;
    }

    if (isEditMode && selectedAssignedSubject) {
      updateAssignedSubjectMutation.mutate({
        academicSessionId: activeAcademicSession.id,
        subjectAssignmentId: selectedAssignedSubject.id,
        payload: data,
      });
      cancelEdit();
      return;
    }

    createAssignedSubjectMutation.mutate({
      academicSessionId: activeAcademicSession.id,
      payload: data,
    });

    resetForm({
      subjectId: "",
      subjectTeacherId: "",
    });
  };

  useEffect(() => {
    if (isEditMode && selectedAssignedSubject) {
      const selectedClass = classesData?.items.find(
        (c) => c.id === selectedAssignedSubject.class.id,
      );
      setSelectedClass(selectedClass ?? null);
      resetForm({
        classSectionId: selectedAssignedSubject.section.id,
        subjectId: selectedAssignedSubject.subject.id,
        subjectTeacherId: selectedAssignedSubject.subjectTeacher.id,
      });
    }
  }, [isEditMode, selectedAssignedSubject, resetForm, classesData]);
  return (
    <ResourcePage>
      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={isEditMode}
        onFormSubmit={handleSubmit(onSubmit)}
        onCancelEdit={cancelEdit}
        resourceLabel="Subject Assignment"
        styles={{ submitButton: "justify-self-start" }}
      >
        <SelectField
          name="class"
          onChange={handleClassChange}
          label="Class"
          value={selectedClass?.id ?? ""}
          errorMessage={fieldErrors.subjectId?.message}
          options={classesData?.items.map((classItem) => ({
            value: classItem.id,
            label: classItem.name,
          }))}
        />

        <SelectField
          name="classSectionId"
          label="Class Section"
          register={register}
          errorMessage={fieldErrors.classSectionId?.message}
          options={selectedClass?.sections.map((section) => ({
            value: section.id,
            label: section.name,
          }))}
        />

        <SelectField
          name="subjectId"
          label="Subject"
          register={register}
          errorMessage={fieldErrors.subjectId?.message}
          options={subjectsData?.items.map((subject) => ({
            value: subject.id,
            label: subject.name,
          }))}
        />

        <SelectField
          name="subjectTeacherId"
          label="Subject Teacher"
          register={register}
          errorMessage={fieldErrors.subjectTeacherId?.message}
          options={teachersData?.items.map((teacher) => ({
            value: teacher.id,
            label: teacher.name,
          }))}
        />
      </ResourceCreationForm>

      <ResourceListingTable
        records={assignedSubjectsData?.items}
        totalRecords={assignedSubjectsData?.total}
        isDataLoading={false}
        searchLabel="class"
        searchFieldValue={(record) => record.class.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Class", render: (s) => s.class.name },
          { label: "Class Section", render: (s) => s.section.name },
          { label: "Subject", render: (s) => s.subject.name },
          { label: "Subject Teacher", render: (s) => s.subjectTeacher.name },
        ]}
        onView={(subjectAssignment) => {
          setSelectedAssignedSubject(subjectAssignment);
          setIsViewModalOpen(true);
        }}
      />

      {/* View Subject Assignment Modal */}
      {isViewModalOpen && selectedAssignedSubject && (
        <ViewRecordModal
          title="Subject Assignment Details"
          subtitle={selectedAssignedSubject.subject.name}
          record={selectedAssignedSubject}
          columns={[
            { label: "Class", render: (s) => s.class.name },
            { label: "Class Section", render: (s) => s.section.name },
            { label: "Subject", render: (s) => s.subject.name },
            { label: "Subject Teacher", render: (s) => s.subjectTeacher.name },
            {
              label: "Created At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedAssignedSubject);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedAssignedSubject(null);
          }}
        />
      )}
    </ResourcePage>
  );
};
