import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createSubjectAssignment,
  fetchAllSubjectAssignments,
  updateSubjectAssignment,
} from "./assign-subjects.service";
import type { PaginationParams } from "@/common/types/global.types";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";

const getAssignSubjectsQueryKey = (
  academicSessionId?: string,
  params?: PaginationParams,
) =>
  params
    ? ["assigned-subjects", academicSessionId, params.offset, params.limit]
    : ["assigned-subjects", academicSessionId];

export function useAssignedSubjects(
  academicSessionId?: string,
  params?: PaginationParams,
) {
  return useQuery({
    queryKey: getAssignSubjectsQueryKey(academicSessionId, params),
    queryFn: async () => {
      if (academicSessionId) {
        return fetchAllSubjectAssignments(academicSessionId, params);
      }
    },
    enabled: <PERSON><PERSON><PERSON>(academicSessionId),
  });
}

export function useCreateAssignedSubject(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createSubjectAssignment,
    onSuccess: async () => {
      notifyResourceActionSuccess("Subject assigned", "create");
      await queryClient.invalidateQueries({
        queryKey: getAssignSubjectsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useUpdateAssignedSubject(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateSubjectAssignment,
    onSuccess: async () => {
      notifyResourceActionSuccess("Subject assigned", "update");
      await queryClient.invalidateQueries({
        queryKey: getAssignSubjectsQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
