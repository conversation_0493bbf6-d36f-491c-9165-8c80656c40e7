import { sendApiRequest } from "@/common/services/api.service";
import type {
  PaginatedApiResponse,
  PaginationParams,
} from "@/common/types/global.types";
import { logger } from "@/lib/logger";
import type {
  CreateSubjectAssignmentPayload,
  SubjectAssignment,
} from "../types/assign-subjects.type";

export async function fetchAllSubjectAssignments(
  academicSessionId: string,
  params?: PaginationParams,
) {
  try {
    return await sendApiRequest<PaginatedApiResponse<SubjectAssignment>>(
      `/academic-sessions/${academicSessionId}/class-section-subjects`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching subject assignments", error);
    throw error;
  }
}

type CreateSubjectAssignmentParams = {
  academicSessionId: string;
  payload: CreateSubjectAssignmentPayload;
};
export async function createSubjectAssignment({
  academicSessionId,
  payload,
}: CreateSubjectAssignmentParams) {
  try {
    return await sendApiRequest(
      `/academic-sessions/${academicSessionId}/class-section-subjects`,
      {
        method: "POST",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error creating subject assignment", error);
    throw error;
  }
}

type UpdateSubjectAssignmentParams = {
  academicSessionId: string;
  subjectAssignmentId: string;
  payload: CreateSubjectAssignmentPayload;
};
export async function updateSubjectAssignment({
  academicSessionId,
  subjectAssignmentId,
  payload,
}: UpdateSubjectAssignmentParams) {
  try {
    return await sendApiRequest(
      `/academic-sessions/${academicSessionId}/class-section-subjects/${subjectAssignmentId}`,
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating subject assignment", error);
    throw error;
  }
}
