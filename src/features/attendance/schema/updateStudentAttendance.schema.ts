import { dateTimeSchema } from "@/common/schemas/zod-common.schemas";
import { z } from "zod";

export const updateStudentAttendanceSchema = z.object({
  status: z
    .enum(["PRESENT", "ABSENT", "LATE", "LEAVE"], {
      message: "Status must be one of 'PRESENT', 'ABSENT', 'LATE', or 'LEAVE'.",
    })
    .optional(),
  remarks: z
    .string()
    .max(100, { message: "Remarks must be less than 100 characters" })
    .optional(),
  checkedInTime: dateTimeSchema.optional(),
  checkedOutTime: dateTimeSchema.optional(),
});
