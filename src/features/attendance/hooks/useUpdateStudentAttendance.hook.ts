import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateStudentAttendance } from "../services/student-attendance.service";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { showErrorNotification } from "@/utils/exception.utils";
import { getStudentAttendanceQueryKey } from "./useStudentAttendance.hook";

export const useUpdateStudentAttendance = (sectionId?: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateStudentAttendance,
    onSuccess: async () => {
      notifyResourceActionSuccess("Student Attendance", "update");
      await queryClient.invalidateQueries({
        queryKey: getStudentAttendanceQueryKey(sectionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
};
