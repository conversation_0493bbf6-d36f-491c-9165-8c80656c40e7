import type { updateStudentAttendanceSchema } from "../schema/updateStudentAttendance.schema";
import { z } from "zod";

export interface StudentAttendance {
  id: string;
  date: string;
  status: "PRESENT" | "ABSENT" | "LATE" | "LEAVE";
  remarks: string;
  createdAt: string;
  checkedInTime: string | null;
  checkedOutTime: string | null;
  student: {
    id: string;
    name: string;
    rollNumber: number;
    enrollmentId: string;
  };
  classSection: {
    id: string;
    name: string;
    totalStudents: number;
    class: {
      id: string;
      name: string;
    };
  };
}

export type UpdateStudentAttendancePayload = z.infer<
  typeof updateStudentAttendanceSchema
>;
