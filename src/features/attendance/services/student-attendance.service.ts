import { sendApiRequest } from "@/common/services/api.service";
import type {
  StudentAttendance,
  UpdateStudentAttendancePayload,
} from "../types/student-attendance.type";
import type {
  PaginatedApiResponse,
  PaginationParams,
} from "@/common/types/global.types";

interface FetchStudentAttendanceParams extends Partial<PaginationParams> {
  sectionId: string;
  status?: StudentAttendance["status"];
  date?: string;
}

export const fetchStudentAttendance = async ({
  sectionId,
  status,
  date,
  offset,
  limit,
}: FetchStudentAttendanceParams): Promise<
  PaginatedApiResponse<StudentAttendance>
> => {
  try {
    const params: Record<string, string | number> = {};
    if (status) params.status = status;
    if (date) params.date = date;
    if (offset !== undefined) params.offset = offset;
    if (limit !== undefined) params.limit = limit;

    return await sendApiRequest<PaginatedApiResponse<StudentAttendance>>(
      `/class-sections/${sectionId}/student-attendances`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    console.error("Error fetching student attendance", error);
    throw error;
  }
};

export const updateStudentAttendance = async ({
  attendanceId,
  payload,
}: {
  attendanceId: string;
  payload: UpdateStudentAttendancePayload;
}): Promise<void> => {
  try {
    await sendApiRequest(`/student-attendances/${attendanceId}`, {
      method: "PATCH",
      withAuthorization: true,
      data: payload,
    });
  } catch (error: unknown) {
    console.error("Error updating student attendance", error);
    throw error;
  }
};
