import { ReactNode } from "react";

interface ExamStatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: ReactNode;
  color: "primary" | "secondary" | "accent" | "success" | "warning" | "error";
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export const ExamStatsCard = ({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color, 
  trend 
}: ExamStatsCardProps) => {
  const colorClasses = {
    primary: "bg-primary/10 text-primary border-primary/20",
    secondary: "bg-secondary/10 text-secondary border-secondary/20",
    accent: "bg-accent/10 text-accent border-accent/20",
    success: "bg-success/10 text-success border-success/20",
    warning: "bg-warning/10 text-warning border-warning/20",
    error: "bg-error/10 text-error border-error/20",
  };

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300 hover:shadow-xl transition-all duration-300">
      <div className="card-body p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-base-content/70 mb-1">
              {title}
            </h3>
            <div className="text-3xl font-bold text-base-content mb-1">
              {value}
            </div>
            {subtitle && (
              <p className="text-sm text-base-content/60">{subtitle}</p>
            )}
            {trend && (
              <div className="flex items-center mt-2">
                <span className={`text-xs font-medium ${
                  trend.isPositive ? 'text-success' : 'text-error'
                }`}>
                  {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
                </span>
                <span className="text-xs text-base-content/60 ml-1">
                  vs last period
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </div>
    </div>
  );
};
