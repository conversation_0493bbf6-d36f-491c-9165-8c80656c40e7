import { useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import type { ExamSchedule } from "../../exams-schedule/types/exams-schedule.type";

interface ExamCalendarViewProps {
  schedules: ExamSchedule[];
  isLoading?: boolean;
}

export const ExamCalendarView = ({ schedules, isLoading }: ExamCalendarViewProps) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
  const firstDayOfWeek = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const getSchedulesForDate = (date: number) => {
    const targetDate = new Date(currentYear, currentMonth, date);
    return schedules.filter(schedule => {
      const scheduleDate = new Date(schedule.date);
      return scheduleDate.toDateString() === targetDate.toDateString();
    });
  };

  const renderCalendarDays = () => {
    const days = [];
    
    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push(<div key={`empty-${i}`} className="h-24"></div>);
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const daySchedules = getSchedulesForDate(day);
      const isToday = new Date().toDateString() === new Date(currentYear, currentMonth, day).toDateString();
      
      days.push(
        <div 
          key={day} 
          className={`h-24 border border-base-300 p-1 ${
            isToday ? 'bg-primary/5 border-primary/30' : 'hover:bg-base-200/50'
          } transition-colors`}
        >
          <div className={`text-sm font-medium mb-1 ${
            isToday ? 'text-primary' : 'text-base-content'
          }`}>
            {day}
          </div>
          <div className="space-y-1">
            {daySchedules.slice(0, 2).map((schedule, index) => (
              <div 
                key={schedule.id}
                className="text-xs p-1 rounded bg-primary/10 text-primary truncate"
                title={`${schedule.subject.name} - ${schedule.classSection.class.name} ${schedule.classSection.name}`}
              >
                {schedule.subject.name}
              </div>
            ))}
            {daySchedules.length > 2 && (
              <div className="text-xs text-base-content/60">
                +{daySchedules.length - 2} more
              </div>
            )}
          </div>
        </div>
      );
    }

    return days;
  };

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-8 w-48 mb-4"></div>
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 35 }).map((_, i) => (
              <div key={i} className="skeleton h-24"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold">
            {monthNames[currentMonth]} {currentYear}
          </h3>
          <div className="flex space-x-2">
            <button 
              onClick={() => navigateMonth('prev')}
              className="btn btn-sm btn-ghost"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <button 
              onClick={() => navigateMonth('next')}
              className="btn btn-sm btn-ghost"
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="h-8 flex items-center justify-center text-sm font-medium text-base-content/70">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {renderCalendarDays()}
        </div>

        <div className="mt-4 flex items-center space-x-4 text-xs text-base-content/60">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded bg-primary/10 mr-2"></div>
            Exam scheduled
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded bg-primary/30 mr-2"></div>
            Today
          </div>
        </div>
      </div>
    </div>
  );
};
