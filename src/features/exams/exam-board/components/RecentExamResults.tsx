import {
  ChartBarIcon,
  TrophyIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import type { ExamResult } from "../../exams-results/types/exam-results.type";

interface RecentExamResultsProps {
  results: ExamResult[];
  isLoading?: boolean;
}

interface ResultSummary {
  totalStudents: number;
  passedStudents: number;
  failedStudents: number;
  absentStudents: number;
  averageMarks: number;
  highestMarks: number;
  lowestMarks: number;
}

export const RecentExamResults = ({
  results,
  isLoading,
}: RecentExamResultsProps) => {
  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="space-y-4">
            <div className="skeleton h-20 w-full"></div>
            <div className="skeleton h-32 w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  const calculateSummary = (results: ExamResult[]): ResultSummary => {
    const totalStudents = results.length;
    const absentStudents = results.filter((r) => r.isAbsent).length;
    const presentStudents = results.filter((r) => !r.isAbsent);
    const passedStudents = presentStudents.filter(
      (r) => (r.marksObtained ?? 0) >= r.passingMarks
    ).length;
    const failedStudents = presentStudents.length - passedStudents;

    const totalMarks = presentStudents.reduce(
      (sum, r) => sum + (r.marksObtained ?? 0),
      0
    );
    const averageMarks =
      presentStudents.length > 0 ? totalMarks / presentStudents.length : 0;

    const marksArray = presentStudents.map((r) => r.marksObtained ?? 0);
    const highestMarks = marksArray.length > 0 ? Math.max(...marksArray) : 0;
    const lowestMarks = marksArray.length > 0 ? Math.min(...marksArray) : 0;

    return {
      totalStudents,
      passedStudents,
      failedStudents,
      absentStudents,
      averageMarks,
      highestMarks,
      lowestMarks,
    };
  };

  interface GroupedExamResult {
    subject: string;
    date: string;
    totalMarks: number;
    passingMarks: number;
    classSection: {
      id: string;
      name: string;
      class: {
        id: string;
        name: string;
      };
    };
    results: ExamResult[];
  }

  // Group results by subject and date
  const groupedResults = results.reduce<Record<string, GroupedExamResult>>(
    (acc, result) => {
      const key = `${result.subject}-${result.date}`;
      if (!acc[key]) {
        acc[key] = {
          subject: result.subject,
          date: result.date,
          totalMarks: result.totalMarks,
          passingMarks: result.passingMarks,
          classSection: result.classSection,
          results: [],
        };
      }
      acc[key].results.push(result);
      return acc;
    },
    {}
  );

  const recentExams = Object.values(groupedResults)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 3);

  if (recentExams.length === 0) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
            <ChartBarIcon className="w-5 h-5 mr-2 text-primary" />
            Recent Exam Results
          </h3>
          <div className="text-center py-8">
            <ChartBarIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
            <p className="text-base-content/60">No exam results available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2 text-primary" />
          Recent Exam Results
        </h3>

        <div className="space-y-6">
          {recentExams.map((exam, index) => {
            const summary = calculateSummary(exam.results);
            const passPercentage =
              summary.totalStudents > 0
                ? (summary.passedStudents /
                    (summary.totalStudents - summary.absentStudents)) *
                  100
                : 0;

            return (
              <div
                key={index}
                className="border border-base-300 rounded-lg p-4"
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-base-content">
                      {exam.subject}
                    </h4>
                    <p className="text-sm text-base-content/70">
                      {exam.classSection.class.name} - {exam.classSection.name}
                    </p>
                    <p className="text-xs text-base-content/60">
                      {new Date(exam.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div
                      className={`badge ${
                        passPercentage >= 80
                          ? "badge-success"
                          : passPercentage >= 60
                            ? "badge-warning"
                            : "badge-error"
                      }`}
                    >
                      {passPercentage.toFixed(1)}% Pass Rate
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-base-content">
                      {summary.totalStudents}
                    </div>
                    <div className="text-xs text-base-content/60">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-success">
                      {summary.passedStudents}
                    </div>
                    <div className="text-xs text-base-content/60">Passed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-error">
                      {summary.failedStudents}
                    </div>
                    <div className="text-xs text-base-content/60">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-warning">
                      {summary.absentStudents}
                    </div>
                    <div className="text-xs text-base-content/60">Absent</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <TrophyIcon className="w-4 h-4 mr-1 text-warning" />
                      Highest: {summary.highestMarks}
                    </span>
                    <span className="flex items-center">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1 text-error" />
                      Lowest: {summary.lowestMarks}
                    </span>
                  </div>
                  <span className="text-base-content/70">
                    Avg: {summary.averageMarks.toFixed(1)}
                  </span>
                </div>

                {/* Progress bar for pass rate */}
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-base-content/60 mb-1">
                    <span>Pass Rate</span>
                    <span>{passPercentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-base-300 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        passPercentage >= 80
                          ? "bg-success"
                          : passPercentage >= 60
                            ? "bg-warning"
                            : "bg-error"
                      }`}
                      style={{ width: `${Math.min(passPercentage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
