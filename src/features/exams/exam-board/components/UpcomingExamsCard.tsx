import { formatDateInLocalFormat } from "@/utils/utils";
import { CalendarIcon, ClockIcon, AcademicCapIcon } from "@heroicons/react/24/outline";
import type { ExamSchedule } from "../../exams-schedule/types/exams-schedule.type";

interface UpcomingExamsCardProps {
  schedules: ExamSchedule[];
  isLoading?: boolean;
}

export const UpcomingExamsCard = ({ schedules, isLoading }: UpcomingExamsCardProps) => {
  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
            <CalendarIcon className="w-5 h-5 mr-2 text-primary" />
            Upcoming Exams
          </h3>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="skeleton h-16 w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const upcomingSchedules = schedules
    .filter(schedule => new Date(schedule.date) >= new Date())
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5);

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
          <CalendarIcon className="w-5 h-5 mr-2 text-primary" />
          Upcoming Exams
          <span className="badge badge-primary badge-sm ml-2">
            {upcomingSchedules.length}
          </span>
        </h3>
        
        {upcomingSchedules.length === 0 ? (
          <div className="text-center py-8">
            <AcademicCapIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
            <p className="text-base-content/60">No upcoming exams scheduled</p>
          </div>
        ) : (
          <div className="space-y-3">
            {upcomingSchedules.map((schedule) => {
              const daysUntil = Math.ceil(
                (new Date(schedule.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
              );
              
              return (
                <div 
                  key={schedule.id} 
                  className="p-4 rounded-lg border border-base-300 hover:border-primary/30 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-base-content mb-1">
                        {schedule.subject.name}
                      </h4>
                      <div className="flex items-center text-sm text-base-content/70 space-x-4">
                        <span className="flex items-center">
                          <CalendarIcon className="w-4 h-4 mr-1" />
                          {formatDateInLocalFormat(schedule.date)}
                        </span>
                        <span className="flex items-center">
                          <ClockIcon className="w-4 h-4 mr-1" />
                          {new Date(schedule.startTime).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                      <div className="text-xs text-base-content/60 mt-1">
                        {schedule.classSection.class.name} - {schedule.classSection.name}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`badge badge-sm ${
                        daysUntil <= 1 ? 'badge-error' : 
                        daysUntil <= 3 ? 'badge-warning' : 
                        'badge-info'
                      }`}>
                        {daysUntil === 0 ? 'Today' : 
                         daysUntil === 1 ? 'Tomorrow' : 
                         `${daysUntil} days`}
                      </div>
                      <div className="text-xs text-base-content/60 mt-1">
                        {schedule.totalMarks} marks
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
