import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { InputField } from "@/common/components/ui/form/InputField";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";
import { notify } from "@/lib/notify";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { formatDateInLocalFormat } from "@/utils/utils";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import { ResourcePage } from "@/common/components/ResourcePage";
import type {
  CreateExamSchedulePayload,
  ExamSchedule,
  UpdateExamSchedulePayload,
} from "../types/exams-schedule.type";
import {
  useCreateExamSchedule,
  useExamSchedules,
  useUpdateExamSchedule,
} from "../service/exams-schedule-query";
import {
  createExamScheduleSchema,
  updateExamScheduleSchema,
} from "../schema/exams-schedule.schema";
import { EXAM_SCHEDULE_FORM_DEFAULT_VALUES } from "../constants/exam-schedule-constants";
import { useExams } from "../../create-exams/services/exam-query";
import { useClassSectionsForExam } from "../../class-section-exams/services/class-section-exams-query";
import { useAssignedSubjects } from "@/features/assign-subjects/service/assign-subjects-query";
import type { Exam } from "../../create-exams/types/exam.type";
import type { ClassSectionExam } from "../../class-section-exams/types/class-section-exams.type";

export const ExamSchedulesPage = () => {
  // State for pagination
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  // State for form mode and view modal
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedExamSchedule, setSelectedExamSchedule] =
    useState<ExamSchedule | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // State for selections
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [selectedClassSectionExam, setSelectedClassSectionExam] =
    useState<ClassSectionExam | null>(null);

  // Get active academic session
  const { activeAcademicSession } = useAcademicSessionStore();

  // Fetch data
  const { data: examsData } = useExams(activeAcademicSession?.id);
  const { data: classSectionExamsData } = useClassSectionsForExam(
    selectedExam?.id,
  );
  const { data: assignedSubjectsData } = useAssignedSubjects(
    activeAcademicSession?.id,
  );
  const { data: examSchedulesData, isLoading } = useExamSchedules(
    selectedClassSectionExam?.id,
  );

  // Mutations for create and update
  const createExamScheduleMutation = useCreateExamSchedule(
    selectedClassSectionExam?.id,
  );
  const updateExamScheduleMutation = useUpdateExamSchedule(
    selectedClassSectionExam?.id,
  );

  // Determine which schema to use based on mode
  const schema = isEditMode
    ? updateExamScheduleSchema
    : createExamScheduleSchema;
  type FormData = z.infer<typeof schema>;

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: EXAM_SCHEDULE_FORM_DEFAULT_VALUES,
  });

  // Reset form when switching between add/edit modes
  useEffect(() => {
    if (isEditMode && selectedExamSchedule) {
      reset({
        date: selectedExamSchedule.date,
        startTime: selectedExamSchedule.startTime.toString(),
        endTime: selectedExamSchedule.endTime.toString(),
        sectionSubjectId: selectedExamSchedule.subject.id,
        totalMarks: selectedExamSchedule.totalMarks,
        passingMarks: selectedExamSchedule.passingMarks,
      });
    } else {
      reset(EXAM_SCHEDULE_FORM_DEFAULT_VALUES);
    }
  }, [isEditMode, selectedExamSchedule, reset]);

  // Handle form submission
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!activeAcademicSession) {
      notify.error("No active academic session found");
      return;
    }

    if (!selectedClassSectionExam) {
      notify.error("Please select a class section exam first");
      return;
    }

    try {
      if (isEditMode && selectedExamSchedule) {
        await updateExamScheduleMutation.mutateAsync({
          classSectionExamId: selectedClassSectionExam.id,
          examScheduleId: selectedExamSchedule.id,
          payload: data as UpdateExamSchedulePayload,
        });
      } else {
        await createExamScheduleMutation.mutateAsync({
          classSectionExamId: selectedClassSectionExam.id,
          payload: data as CreateExamSchedulePayload,
        });
      }

      // Reset form and refresh data
      cancelEdit();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleEdit = (examSchedule: ExamSchedule) => {
    setSelectedExamSchedule(examSchedule);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedExamSchedule(null);
    reset(EXAM_SCHEDULE_FORM_DEFAULT_VALUES);
  };

  const handleExamChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const examId = e.target.value;
    const exam = examsData?.items.find((e) => e.id === examId);
    setSelectedExam(exam ?? null);
    setSelectedClassSectionExam(null); // Reset class section selection
  };

  const handleClassSectionExamChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const classSectionExamId = e.target.value;
    const classSectionExam = classSectionExamsData?.items.find(
      (cse) => cse.id === classSectionExamId,
    );
    setSelectedClassSectionExam(classSectionExam ?? null);
  };

  // Filter assigned subjects based on selected class section
  const filteredAssignedSubjects =
    assignedSubjectsData?.items.filter(
      (assignment) =>
        selectedClassSectionExam &&
        assignment.section.id === selectedClassSectionExam.classSection.id,
    ) ?? [];

  // If no active academic session, show message
  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <ResourcePage>
      {/* Selection Controls */}
      <div className="bg-base-200 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-4">
          Select Exam and Class Section
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectField
            name="examSelection"
            onChange={handleExamChange}
            label="Select Exam"
            value={selectedExam?.id ?? ""}
            defaultOptionLabel="-- Select Exam --"
            options={examsData?.items.map((exam) => ({
              value: exam.id,
              label: exam.name,
            }))}
          />

          <SelectField
            name="classSectionExamSelection"
            onChange={handleClassSectionExamChange}
            label="Select Class Section"
            value={selectedClassSectionExam?.id ?? ""}
            disabled={!selectedExam}
            defaultOptionLabel="-- Select Class Section --"
            options={classSectionExamsData?.items.map((cse) => ({
              value: cse.id,
              label: `${cse.classSection.class.name} - ${cse.classSection.name}`,
            }))}
          />
        </div>
      </div>

      {/* Form for creating/editing exam schedules */}

      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={isEditMode}
        onFormSubmit={handleSubmit(onSubmit)}
        onCancelEdit={cancelEdit}
        resourceLabel="Exam Schedule"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            placeholder="2025-01-15"
            register={register}
            name="date"
            label="Exam Date"
            type="date"
            errorMessage={fieldErrors.date?.message}
          />

          <SelectField
            register={register}
            name="sectionSubjectId"
            label="Subject"
            defaultOptionLabel="-- Select Subject --"
            errorMessage={fieldErrors.sectionSubjectId?.message}
            options={filteredAssignedSubjects.map((assignment) => ({
              value: assignment.id,
              label: assignment.subject.name,
            }))}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            placeholder="09:00"
            register={register}
            name="startTime"
            label="Start Time"
            type="time"
            errorMessage={fieldErrors.startTime?.message}
          />

          <InputField
            placeholder="11:00"
            register={register}
            name="endTime"
            label="End Time"
            type="time"
            errorMessage={fieldErrors.endTime?.message}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            placeholder="100"
            register={register}
            name="totalMarks"
            label="Total Marks"
            valueAsNumber
            type="number"
            errorMessage={fieldErrors.totalMarks?.message}
          />

          <InputField
            placeholder="40"
            register={register}
            name="passingMarks"
            valueAsNumber
            label="Passing Marks"
            type="number"
            errorMessage={fieldErrors.passingMarks?.message}
          />
        </div>
      </ResourceCreationForm>

      {/* Exam Schedules Table */}
      {
        <ResourceListingTable
          records={examSchedulesData?.items ?? []}
          totalRecords={examSchedulesData?.total ?? 0}
          isDataLoading={isLoading}
          searchLabel="subject"
          searchFieldValue={(record) => record.subject.name}
          pagination={paginationParams}
          onPaginationChange={setPaginationParams}
          columns={[
            { label: "Date", render: (s) => formatDateInLocalFormat(s.date) },
            { label: "Subject", render: (s) => s.subject.name },
            {
              label: "Start Time",
              render: (s) => new Date(s.startTime).toLocaleTimeString(),
            },
            {
              label: "End Time",
              render: (s) => new Date(s.endTime).toLocaleTimeString(),
            },
            { label: "Total Marks", render: (s) => s.totalMarks },
            { label: "Passing Marks", render: (s) => s.passingMarks },
          ]}
          onView={(item) => {
            setSelectedExamSchedule(item);
            setIsViewModalOpen(true);
          }}
        />
      }

      {/* View Exam Schedule Modal */}
      {isViewModalOpen && selectedExamSchedule && (
        <ViewRecordModal
          title="Exam Schedule Details"
          subtitle={`${selectedExamSchedule.subject.name} - ${formatDateInLocalFormat(selectedExamSchedule.date)}`}
          record={selectedExamSchedule}
          columns={[
            { label: "Date", render: (s) => formatDateInLocalFormat(s.date) },
            { label: "Subject", render: (s) => s.subject.name },
            { label: "Class", render: (s) => s.classSection.class.name },
            { label: "Section", render: (s) => s.classSection.name },
            {
              label: "Start Time",
              render: (s) => new Date(s.startTime).toLocaleTimeString(),
            },
            {
              label: "End Time",
              render: (s) => new Date(s.endTime).toLocaleTimeString(),
            },
            { label: "Total Marks", render: (s) => s.totalMarks },
            { label: "Passing Marks", render: (s) => s.passingMarks },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedExamSchedule);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedExamSchedule(null);
          }}
        />
      )}
    </ResourcePage>
  );
};
