import type {
  createExamResultSchema,
  updateExamResultSchema,
} from "../schemas/exam-results.schema";
import { z } from "zod";

export type CreateExamResultPayload = z.infer<typeof createExamResultSchema>;

export type UpdateExamResultPayload = z.infer<typeof updateExamResultSchema>;

export interface ExamResult {
  id: string;
  date: string;
  subject: string;
  totalMarks: number;
  passingMarks: number;
  createdAt: string;
  remarks?: string | null | undefined;
  student: {
    id: string;
    name: string;
    photo: string | null;
    rollNumber: number;
  };
  marksObtained: number | null;
  isAbsent: boolean;
  classSection: {
    id: string;
    name: string;
    class: {
      id: string;
      name: string;
    };
    isActive: boolean;
    createdAt: string;
    totalStudents: number;
    classTeacher: {
      id: string;
      name: string;
    };
  };
}
