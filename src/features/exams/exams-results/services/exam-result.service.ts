import { sendApiRequest } from "@/common/services/api.service";
import { logger } from "@/lib/logger";
import type {
  CreateExamResultPayload,
  ExamResult,
  UpdateExamResultPayload,
} from "../types/exam-results.type";
import type { PaginatedApiResponse } from "@/common/types/global.types";

export interface CreateExamResultParams {
  examScheduleId: string;
  payload: CreateExamResultPayload;
}

export async function createExamResult({
  examScheduleId,
  payload,
}: CreateExamResultParams) {
  try {
    return await sendApiRequest(`/exam-schedules/${examScheduleId}/results`, {
      method: "POST",
      withAuthorization: true,
      data: payload,
    });
  } catch (error: unknown) {
    logger.error("Error creating exam result", error);
    throw error;
  }
}

export interface FetchAllExamResultsParams {
  examScheduleId: string;
  params?: Partial<{
    enrollmentId: string;
    offset: number;
    limit: number;
  }>;
}

export async function fetchAllExamResults({
  examScheduleId,
  params,
}: FetchAllExamResultsParams) {
  try {
    return await sendApiRequest<PaginatedApiResponse<ExamResult>>(
      `/exam-schedules/${examScheduleId}/results`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching exam results", error);
    throw error;
  }
}

export interface UpdateExamResultParams {
  examScheduleId: string;
  resultId: string;
  payload: UpdateExamResultPayload;
}

export async function updateExamResult({
  examScheduleId,
  resultId,
  payload,
}: UpdateExamResultParams) {
  try {
    return await sendApiRequest(
      `/exam-schedules/${examScheduleId}/results/${resultId}`,
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating exam result", error);
    throw error;
  }
}
