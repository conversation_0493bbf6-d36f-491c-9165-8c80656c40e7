import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createExamResult,
  fetchAllExamResults,
  updateExamResult,
  type FetchAllExamResultsParams,
} from "./exam-result.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";

const getExamResultsQueryKey = (
  examScheduleId?: string,
  params?: FetchAllExamResultsParams["params"],
) =>
  params
    ? [
        "exam-results",
        examScheduleId,
        params.enrollmentId,
        params.offset,
        params.limit,
      ]
    : ["exam-results", examScheduleId];

export function useExamResults(
  examScheduleId?: string,
  params?: FetchAllExamResultsParams["params"],
) {
  return useQuery({
    queryKey: getExamResultsQueryKey(examScheduleId, params),
    queryFn: async () => {
      if (examScheduleId) {
        return fetchAllExamResults({ examScheduleId, params });
      }
    },
    enabled: Boolean(examScheduleId),
  });
}

export function useCreateExamResult(examScheduleId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createExamResult,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam result", "create");
      await queryClient.invalidateQueries({
        queryKey: getExamResultsQueryKey(examScheduleId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useUpdateExamResult(examScheduleId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateExamResult,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam result", "update");
      await queryClient.invalidateQueries({
        queryKey: getExamResultsQueryKey(examScheduleId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
