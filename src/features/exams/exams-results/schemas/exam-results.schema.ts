import { z } from "zod";

export const createExamResultSchema = z.object({
  enrollmentId: z.string().nonempty(),
  marksObtained: z
    .number()
    .positive()
    .max(1000, {
      message: "Marks obtained cannot exceed 1000",
    })
    .nullable(),
  remarks: z.string().nullable().optional(),
  isAbsent: z.boolean().default(false),
});

export const updateExamResultSchema = createExamResultSchema
  .omit({ enrollmentId: true })
  .partial();
