import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { InputField } from "@/common/components/ui/form/InputField";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";
import { notify } from "@/lib/notify";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { formatDateInLocalFormat } from "@/utils/utils";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import type {
  CreateExamPayload,
  Exam,
  UpdateExamPayload,
} from "./types/exam.type";
import { useCreateExam, useExams, useUpdateExam } from "./services/exam-query";
import { createExamSchema, updateExamSchema } from "./schema/exam.schema";
import { EXAM_FORM_DEFAULT_VALUES } from "./constants/exams.constants";

export const ExamsPage = () => {
  // State for pagination
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  // State for form mode and view modal
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Get active academic session
  const { activeAcademicSession } = useAcademicSessionStore();

  // Fetch subjects with pagination
  const {
    data: subjectsResponse,
    isLoading,
    refetch,
  } = useExams(activeAcademicSession?.id, paginationParams);

  // Mutations for create and update
  const createExamMutation = useCreateExam(activeAcademicSession?.id);
  const updateExamMutation = useUpdateExam(activeAcademicSession?.id);

  // Determine which schema to use based on mode
  const schema = isEditMode ? updateExamSchema : createExamSchema;
  type FormData = z.infer<typeof schema>;

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: EXAM_FORM_DEFAULT_VALUES,
  });

  // Reset form when switching between add/edit modes
  useEffect(() => {
    if (isEditMode && selectedExam) {
      reset(selectedExam);
    } else {
      reset(EXAM_FORM_DEFAULT_VALUES);
    }
  }, [isEditMode, selectedExam, reset]);

  // Handle form submission
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!activeAcademicSession) {
      notify.error("No active academic session found");
      return;
    }

    try {
      if (isEditMode && selectedExam) {
        await updateExamMutation.mutateAsync({
          academicSessionId: activeAcademicSession.id,
          examId: selectedExam.id,
          payload: data as UpdateExamPayload,
        });
      } else {
        await createExamMutation.mutateAsync({
          academicSessionId: activeAcademicSession.id,
          payload: data as CreateExamPayload,
        });
      }

      // Reset form and refresh data
      cancelEdit();
      void refetch();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleEdit = (exam: Exam) => {
    setSelectedExam(exam);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedExam(null);
    reset(EXAM_FORM_DEFAULT_VALUES);
  };

  // If no active academic session, show message
  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <div className="space-y-8">
      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={isEditMode}
        onFormSubmit={handleSubmit(onSubmit)}
        onCancelEdit={cancelEdit}
        resourceLabel="Exam"
      >
        <InputField
          placeholder="Final Exam"
          name="name"
          label="Exam Name"
          register={register}
          errorMessage={fieldErrors.name?.message}
        />

        <InputField
          placeholder="2025-01-01"
          register={register}
          name="startDate"
          label="Start Date"
          type="date"
          errorMessage={fieldErrors.startDate?.message}
        />

        <InputField
          placeholder="2025-01-29"
          name="endDate"
          label="End Date"
          type="date"
          register={register}
          errorMessage={fieldErrors.endDate?.message}
        />
      </ResourceCreationForm>

      <ResourceListingTable
        records={subjectsResponse?.items ?? []}
        totalRecords={subjectsResponse?.total ?? 0}
        isDataLoading={isLoading}
        searchLabel="name"
        searchFieldValue={(record) => record.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Name", render: (s) => s.name },
          {
            label: "Start Date",
            render: (s) => formatDateInLocalFormat(s.startDate),
          },
          {
            label: "End Date",
            render: (s) => formatDateInLocalFormat(s.endDate),
          },
        ]}
        onView={(item) => {
          setSelectedExam(item);
          setIsViewModalOpen(true);
        }}
      />

      {/* View Subject Modal */}
      {isViewModalOpen && selectedExam && (
        <ViewRecordModal
          title="Subject Details"
          subtitle={selectedExam.name}
          record={selectedExam}
          columns={[
            { label: "Name", render: (s) => s.name },
            {
              label: "Start Date",
              render: (s) => formatDateInLocalFormat(s.startDate),
            },
            {
              label: "End Date",
              render: (s) => formatDateInLocalFormat(s.endDate),
            },
            {
              label: "Created At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedExam);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedExam(null);
          }}
        />
      )}
    </div>
  );
};
