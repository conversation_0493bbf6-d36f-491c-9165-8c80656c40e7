import { sendApiRequest } from "@/common/services/api.service";
import type {
  PaginatedApiResponse,
  PaginationParams,
} from "@/common/types/global.types";
import { logger } from "@/lib/logger";
import type {
  CreateExamPayload,
  Exam,
  UpdateExamPayload,
} from "../types/exam.type";

type CreateExamParams = {
  academicSessionId: string;
  payload: CreateExamPayload;
};

export async function createExam({
  academicSessionId,
  payload,
}: CreateExamParams) {
  try {
    return await sendApiRequest<Exam>(
      `/academic-sessions/${academicSessionId}/exams`,
      {
        method: "POST",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error creating exam", error);
    throw error;
  }
}

export async function fetchAllExams(
  academicSessionId: string,
  params?: PaginationParams & {
    status?: "upcoming" | "ongoing" | "completed";
    startDate?: string;
    endDate?: string;
    classId?: string;
  },
) {
  try {
    return await sendApiRequest<PaginatedApiResponse<Exam>>(
      `/academic-sessions/${academicSessionId}/exams`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching exams", error);
    throw error;
  }
}

type UpdateExamParams = {
  academicSessionId: string;
  examId: string;
  payload: UpdateExamPayload;
};

export async function updateExam({
  academicSessionId,
  examId,
  payload,
}: UpdateExamParams) {
  try {
    return await sendApiRequest<Exam>(
      `/academic-sessions/${academicSessionId}/exams/${examId}`,
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating exam", error);
    throw error;
  }
}
