import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  assignExamToClassSections,
  fetchAllClassSectionsForExam,
} from "./class-section-exams.service";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";

const getClassSectionExamsQueryKey = (examId?: string) =>
  examId ? ["class-section-exams", examId] : ["class-section-exams"];

export function useClassSectionsForExam(examId?: string) {
  return useQuery({
    queryKey: getClassSectionExamsQueryKey(examId),
    queryFn: async () => {
      if (examId) {
        return fetchAllClassSectionsForExam(examId);
      }
    },
    enabled: Boolean(examId),
  });
}

export function useAssignExamToClassSections(examId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: assignExamToClassSections,
    onSuccess: async () => {
      notifyResourceActionSuccess("Exam assigned to class sections", "create");
      await queryClient.invalidateQueries({
        queryKey: getClassSectionExamsQueryKey(examId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
