import { z } from "zod";
import type { Exam } from "../../create-exams/types/exam.type";
import type { assignExamToClassSectionsSchema } from "../schema/assign-exam-to-class-sections.schema";

export interface ClassSectionExam {
  id: string;
  createdAt: string;
  exam: Exam;
  classSection: {
    id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    class: {
      id: string;
      name: string;
    };
  };
}

export type AssignExamToClassSectionsPayload = z.infer<
  typeof assignExamToClassSectionsSchema
>;
