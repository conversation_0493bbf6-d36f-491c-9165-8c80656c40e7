import { create } from "zustand";
import type { AcademicSession } from "./session.type";

interface SessionState {
  activeAcademicSession: AcademicSession | null;
  setAcademicSession: (session: AcademicSession) => void;
  clearAcademicSession: () => void;
}

export const useAcademicSessionStore = create<SessionState>()((set) => ({
  activeAcademicSession: null,
  setAcademicSession: (session: AcademicSession) => {
    set({ activeAcademicSession: session });
  },
  clearAcademicSession: () => {
    set({ activeAcademicSession: null });
  },
}));
