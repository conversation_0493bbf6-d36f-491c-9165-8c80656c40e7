import { z } from "zod";
import type { createSessionSchema } from "./session.schema";

export type CreateAcademicSessionPayload = z.infer<typeof createSessionSchema>;
export type UpdateAcademicSessionPayload =
  Partial<CreateAcademicSessionPayload>;
export interface AcademicSession {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  branchId: string;
  createdAt: string;
}
