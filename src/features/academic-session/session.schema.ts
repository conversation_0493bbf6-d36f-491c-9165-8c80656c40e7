import { dateSchema } from "@/common/schemas/zod-common.schemas";
import { z } from "zod";

const sessionBaseSchema = z.object({
  name: z.string().nonempty({ message: "Session name is required" }),
  startDate: dateSchema,
  endDate: dateSchema,
  isActive: z.boolean().default(true),
});

export const createSessionSchema = sessionBaseSchema.refine(
  (data) => data.startDate < data.endDate,
  {
    message: "Start date must be before end date",
    path: ["startDate"],
  }
);

export const updateSessionSchema = sessionBaseSchema.partial();
