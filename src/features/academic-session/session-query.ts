import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createAcademicSession,
  fetchAllAcademicSessions,
  updateAcademicSession,
} from "./session.service";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { waitFor } from "@/utils/utils";

function getAcademicSessionQueryKey(branchId?: string) {
  return ["sessions", branchId];
}

export function useAcademicSessions(branchId?: string) {
  return useQuery({
    queryKey: getAcademicSessionQueryKey(branchId),
    queryFn: async () => {
      const sessions = await fetchAllAcademicSessions({ branchId: branchId! });
      return sessions;
    },
    staleTime: Infinity,
    enabled: Boolean(branchId),
  });
}

export function useCreateAcademicSession(branchId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createAcademicSession,
    onSuccess: async () => {
      notifyResourceActionSuccess("Academic Session", "create");
      await queryClient.invalidateQueries({
        queryKey: getAcademicSessionQueryKey(branchId),
      });
    },
  });
}

export function useUpdateAcademicSession(branchId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateAcademicSession,
    onSuccess: async () => {
      notifyResourceActionSuccess("Academic Session", "update");
      await queryClient.invalidateQueries({
        queryKey: getAcademicSessionQueryKey(branchId),
      });
    },
  });
}
