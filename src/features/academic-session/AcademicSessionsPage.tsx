import { motion } from "framer-motion";
import { CalendarIcon } from "@heroicons/react/24/outline";
import { logger } from "@/lib/logger";
import { InputField } from "@/common/components/ui/form/InputField";
import { type SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createSessionSchema } from "./session.schema";
import type { CreateAcademicSessionPayload } from "./session.type";
import { useBranchStore } from "../onboarding/setup/branches/branches.store";
import { useAcademicSessions, useCreateAcademicSession } from "./session-query";
import { ResourceNotFound } from "@/common/components/ResouceNotFound";

export const AcademicSessionsPage = () => {
  const { selectedBranch } = useBranchStore();

  const { data: academicSessions } = useAcademicSessions(selectedBranch?.id);
  const createAcademicSessionMutation = useCreateAcademicSession(
    selectedBranch?.id,
  );
  const sessionForm = useForm<CreateAcademicSessionPayload>({
    resolver: zodResolver(createSessionSchema),
    defaultValues: {
      name: "",
      startDate: "",
      endDate: "",
    },
  });

  const onSubmit: SubmitHandler<CreateAcademicSessionPayload> = (data) => {
    if (!selectedBranch) {
      logger.error("Cannot create session. No branch selected");
      return;
    }

    createAcademicSessionMutation.mutate({
      branchId: selectedBranch.id,
      payload: data,
    });

    sessionForm.reset();
  };

  if (!selectedBranch) {
    return (
      <ResourceNotFound
        resourceName="Branch"
        customMessage="Please select a branch or create a new one"
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col items-center mb-4">
        <h2 className="text-2xl font-bold">Create Session</h2>
        <p className="text-base-content/70 mt-2 text-center">
          Add a new session to your branch
        </p>
      </div>

      <div className="card bg-base-200 shadow-sm">
        <div className="card-body">
          <form
            onSubmit={sessionForm.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <InputField
              type="text"
              label="Session Name*"
              register={sessionForm.register}
              placeholder="e.g., 2025-2026"
              name="name"
            >
              <CalendarIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            </InputField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                type="date"
                label="Start Date*"
                name="startDate"
                register={sessionForm.register}
                errorMessage={sessionForm.formState.errors.startDate?.message}
              ></InputField>

              <InputField
                type="date"
                label="End Date*"
                name="endDate"
                register={sessionForm.register}
                errorMessage={sessionForm.formState.errors.endDate?.message}
              ></InputField>
            </div>

            <div className="flex justify-center gap-3 mt-6">
              <button
                type="submit"
                className="btn btn-primary min-w-40"
                disabled={sessionForm.formState.isSubmitting}
              >
                {sessionForm.formState.isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Creating...
                  </>
                ) : (
                  "Add Session"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Sessions List Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-10"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold">Session</h3>
        </div>
        <div className="space-y-3">
          {academicSessions?.map((session) => (
            <motion.div
              key={session.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`card bg-base-100 shadow-sm border ${session.isActive ? "border-success" : "border-base-200"} hover:shadow-md transition-shadow`}
            >
              <div className="card-body p-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CalendarIcon
                      className={`h-5 w-5 ${session.isActive ? "text-success" : "text-base-content/70"}`}
                    />
                    <h3 className="font-medium">{session.name}</h3>
                    {session.isActive && (
                      <div className="badge badge-success badge-sm">Active</div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2 text-sm">
                  <div>
                    <p className="text-xs text-base-content/70">Start Date</p>
                    <p>{new Date(session.startDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-xs text-base-content/70">End Date</p>
                    <p>{new Date(session.endDate).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
};
