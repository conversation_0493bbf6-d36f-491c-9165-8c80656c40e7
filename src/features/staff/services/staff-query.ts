import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Staff } from "../types/staff.type";
import {
  type FetchStaffParams,
  createStaff,
  fetchAllStaff,
  updateStaff,
} from "./staff.service";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { showErrorNotification } from "@/utils/exception.utils";

const getStaffQueryKey = (
  branchId?: string,
  paginationParams?: FetchStaffParams,
) =>
  paginationParams
    ? ["staff", branchId, paginationParams.offset, paginationParams.limit]
    : ["staff", branchId];

export const useStaffByType = (type: Staff["type"], branchId?: string) => {
  return useQuery({
    queryKey: ["staff", type],
    queryFn: () => {
      if (branchId) {
        return fetchAllStaff(branchId, { role: type });
      }
    },
    enabled: Boolean(branchId),
  });
};

export const useStaff = (
  branchId?: string,
  paginationParams?: FetchStaffParams,
) => {
  return useQuery({
    queryKey: getStaffQueryKey(branchId, paginationParams),
    queryFn: async () => {
      if (branchId) {
        return await fetchAllStaff(branchId, paginationParams);
      }
    },
    refetchOnMount: false,
    enabled: Boolean(branchId),
  });
};

export const useCreateStaff = (branchId?: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createStaff,
    onSuccess: async () => {
      notifyResourceActionSuccess("Staff", "create");
      await queryClient.invalidateQueries({
        queryKey: getStaffQueryKey(branchId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
};

export const useUpdateStaff = (branchId?: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateStaff,
    onSuccess: async () => {
      notifyResourceActionSuccess("Staff", "update");
      await queryClient.invalidateQueries({
        queryKey: getStaffQueryKey(branchId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
};
