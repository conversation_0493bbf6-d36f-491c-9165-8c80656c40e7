import { sendApiRequest } from "@/common/services/api.service";
import type {
  CreateStaffPayload,
  Staff,
  UpdateStaffPayload,
} from "../types/staff.type";
import type {
  PaginatedApiResponse,
  PaginationParams,
} from "@/common/types/global.types";
import { logger } from "@/lib/logger";

export interface FetchStaffParams extends Partial<PaginationParams> {
  role?: Staff["role"];
  department?: Staff["department"];
  search?: string;
}

export async function fetchAllStaff(
  branchId: string,
  params?: FetchStaffParams,
) {
  try {
    return await sendApiRequest<PaginatedApiResponse<Staff>>(
      `/branches/${branchId}/staff`,
      {
        method: "GET",
        withAuthorization: true,
        params,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching staff", error);
    throw error;
  }
}

type CreateStaffParams = {
  branchId: string;
  payload: CreateStaffPayload;
};
export async function createStaff({ branchId, payload }: CreateStaffParams) {
  try {
    return await sendApiRequest<Staff>(`/branches/${branchId}/staff`, {
      method: "POST",
      data: payload,
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.error("Error creating staff", error);
    throw error;
  }
}

type UpdateStaffParams = {
  branchId: string;
  staffId: string;
  payload: UpdateStaffPayload["data"];
};
export async function updateStaff({
  branchId,
  staffId,
  payload,
}: UpdateStaffParams) {
  try {
    return await sendApiRequest<Staff>(
      `/branches/${branchId}/staff/${staffId}`,
      {
        method: "PATCH",
        data: payload,
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating staff", error);
    throw error;
  }
}
