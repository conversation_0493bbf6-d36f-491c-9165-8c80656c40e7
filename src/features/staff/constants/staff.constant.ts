import type { CreateStaffPayload, Staff } from "../types/staff.type";

type DepartmentOption = {
  value: Staff["department"];
  label: string;
  selected?: boolean;
};

type Type = {
  value: Staff["type"];
  label: string;
  department: Staff["department"];
};

export const DEPARTMENT_OPTIONS: DepartmentOption[] = [
  { value: "ACADEMIC", label: "Academic", selected: true },
  { value: "ADMINISTRATION", label: "Administration" },
  { value: "SUPPORT", label: "Support" },
];

export const TYPES: Type[] = [
  { value: "TEACHER", label: "Teacher", department: "ACADEMIC" },
  {
    value: "BRANCH_ADMIN",
    label: "Branch Admin",
    department: "ADMINISTRATION",
  },
  { value: "ACCOUNTANT", label: "Accountant", department: "ADMINISTRATION" },
  { value: "SUPPORT_STAFF", label: "Support Staff", department: "SUPPORT" },
];

export const CREATE_STAFF_DEFAULT_VALUES: CreateStaffPayload = {
  name: "",
  email: "",
  phone: "",
  address: "",
  gender: "MA<PERSON>",
  photo: undefined,
  designation: "",
  department: "ACADEMIC",
  type: "TEACHER",
  salary: 0,
  cnic: "",
  password: null,
};
