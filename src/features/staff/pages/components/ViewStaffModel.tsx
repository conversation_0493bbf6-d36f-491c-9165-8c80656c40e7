import { Button } from "@/common/components/ui/Button";
import { DEPARTMENT_OPTIONS, TYPES } from "../../constants/staff.constant";
import { getProfilePlaceholderImage } from "@/utils/image.utils";
import { motion } from "framer-motion";
import type { Staff } from "../../types/staff.type";
import { CircleXIcon } from "lucide-react";

export interface ViewStaffModelProps {
  staff: Staff;
  onClose: () => void;
  onUpdate: () => void;
}

const ViewStaffModel = ({ staff, onClose, onUpdate }: ViewStaffModelProps) => {
  return (
    <div className="fixed inset-0 bg-semi-transparent bg-opacity-60 flex justify-center items-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-base-100 rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative"
      >
        <button
          className="absolute cursor-pointer top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
          onClick={onClose}
        >
          <CircleXIcon />
        </button>

        <div className="p-6">
          <div className="text-center mb-6">
            <div className="avatar">
              <div className="w-24 h-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 mx-auto">
                <img
                  src={staff.photo ?? getProfilePlaceholderImage()}
                  alt={staff.name}
                />
              </div>
            </div>
            <h2 className="text-2xl font-bold mt-4">{staff.name}</h2>
            <p className="text-sm text-gray-500">
              {staff.designation} •{" "}
              {TYPES.find((r) => r.value === staff.role)?.label}
            </p>
          </div>

          <div className="divider"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="font-semibold text-lg">Personal Information</h3>
              <p>
                <span className="font-medium">CNIC:</span> {staff.cnic}
              </p>
              <p>
                <span className="font-medium">Gender:</span> {staff.gender}
              </p>
              <p>
                <span className="font-medium">Address:</span> {staff.address}
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-lg">Contact Information</h3>
              <p>
                <span className="font-medium">Email:</span> {staff.email}
              </p>
              <p>
                <span className="font-medium">Phone:</span> {staff.phone}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="font-semibold text-lg">Employment Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <p>
                <span className="font-medium">Department:</span>{" "}
                {
                  DEPARTMENT_OPTIONS.find((d) => d.value === staff.department)
                    ?.label
                }
              </p>
              <p>
                <span className="font-medium">Role:</span>{" "}
                {TYPES.find((r) => r.value === staff.role)?.label}
              </p>
              <p>
                <span className="font-medium">Designation:</span>{" "}
                {staff.designation}
              </p>
              <p>
                <span className="font-medium">Salary:</span>{" "}
                {staff.salary.toLocaleString()}
              </p>
              <p>
                <span className="font-medium">Joined:</span>{" "}
                {new Date(staff.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="mt-8 flex justify-center">
            <Button shape="primary" onClick={onUpdate} className="px-6">
              Update
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ViewStaffModel;
