import { ResourcePage } from "@/common/components/ResourcePage";
import { Button } from "@/common/components/ui/Button";
import { InputField } from "@/common/components/ui/form/InputField";
import { UploadImage } from "@/common/components/ui/UploadImge";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { createStaffSchema, updateStaffSchema } from "../schema/staff.schema";
import type { CreateStaffPayload, Staff } from "../types/staff.type";
import { zodResolver } from "@hookform/resolvers/zod";
import { type Resolver, type SubmitHandler, useForm } from "react-hook-form";
import { uploadFile } from "@/common/services/upload.service";
import { logger } from "@/lib/logger";
import { motion } from "framer-motion";
import { getProfilePlaceholderImage } from "@/utils/image.utils";
import { SelectField } from "@/common/components/ui/form/SelectField";
import {
  CREATE_STAFF_DEFAULT_VALUES,
  DEPARTMENT_OPTIONS,
  TYPES,
} from "../constants/staff.constant";
import {
  useCreateStaff,
  useStaff,
  useUpdateStaff,
} from "../services/staff-query";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import ViewStaffModel from "./components/ViewStaffModel";

export const StaffPage = () => {
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("ACADEMIC");
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(
    null,
  );
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Fetch staff data on component mount
  const { selectedBranch } = useBranchStore();
  const { data: staffData, isFetching } = useStaff(
    selectedBranch?.id,
    paginationParams,
  );

  // Mutations for create and update
  const createStaffMutation = useCreateStaff(selectedBranch?.id);
  const updateStaffMutation = useUpdateStaff(selectedBranch?.id);

  // Form setup
  const schema = isEditMode ? updateStaffSchema : createStaffSchema;
  const {
    register,
    reset: resetForm,
    formState: { errors: fieldErrors, isSubmitting: isFormSubmitting },
    handleSubmit,
  } = useForm<CreateStaffPayload>({
    mode: "onChange",
    resolver: zodResolver(schema) as unknown as Resolver<CreateStaffPayload>,
    defaultValues: CREATE_STAFF_DEFAULT_VALUES,
  });

  console.log(fieldErrors);
  const uploadProfilePicture = async (file: File) => {
    try {
      const { url } = await uploadFile(file, "image");
      return url;
    } catch (error) {
      logger.error("Error uploading profile picture:", error);
      throw error;
    }
  };

  const onSubmit: SubmitHandler<CreateStaffPayload> = async (data) => {
    if (!selectedBranch) {
      logger.error("Cannot create staff. No branch selected");
      return;
    }

    try {
      if (profilePictureFile) {
        const url = await uploadProfilePicture(profilePictureFile);
        data.photo = url;
      }

      if (isEditMode && selectedStaff) {
        await updateStaffMutation.mutateAsync({
          branchId: selectedBranch.id,
          staffId: selectedStaff.id,
          payload: data,
        });
      } else {
        await createStaffMutation.mutateAsync({
          branchId: selectedBranch.id,
          payload: data,
        });
      }
      setProfilePictureFile(null);
      setIsEditMode(false);
      resetForm(CREATE_STAFF_DEFAULT_VALUES);
    } catch (error: unknown) {
      logger.error("Failed to create staff", error);
    }
  };

  const getDepartmentRoles = (department: string) => {
    return TYPES.filter((role) => role.department === department);
  };

  const onDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const department = e.target.value;
    setSelectedDepartment(department);
  };

  const handleEdit = () => {
    setSelectedStaff(selectedStaff);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedStaff(null);
    resetForm(CREATE_STAFF_DEFAULT_VALUES);
  };

  useEffect(() => {
    if (isEditMode && selectedStaff) {
      resetForm(selectedStaff);
    } else {
      resetForm(CREATE_STAFF_DEFAULT_VALUES);
    }
  }, [isEditMode, selectedStaff, resetForm]);

  return (
    <ResourcePage>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="p-6">
          <div className="card bg-base-200 shadow-sm">
            <div className="card-body">
              <form>
                {/* Personal Info Section */}
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-[#0f243f] flex items-center gap-2">
                    <DocumentTextIcon className="h-5 w-5" />
                    Personal Details
                  </h3>
                  <UploadImage
                    initialPreview={
                      selectedStaff?.photo ?? getProfilePlaceholderImage()
                    }
                    onImageSelected={setProfilePictureFile}
                    buttonText="Upload Photo"
                    previewSize="medium"
                  />

                  {isEditMode && (
                    <div className="flex">
                      <Button
                        shape="neutral"
                        onClick={cancelEdit}
                        className="btn-sm ml-auto"
                      >
                        Cancel Edit
                      </Button>
                    </div>
                  )}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <InputField
                      placeholder="John Doe"
                      name="name"
                      label="Full Name"
                      register={register}
                      errorMessage={fieldErrors.name?.message}
                    />
                    <SelectField
                      register={register}
                      name="gender"
                      label="Gender"
                      errorMessage={fieldErrors.gender?.message}
                      options={[
                        { value: "MALE", label: "Male" },
                        { value: "FEMALE", label: "Female" },
                        { value: "OTHER", label: "Other" },
                      ]}
                    />
                    <InputField
                      placeholder="55000"
                      name="salary"
                      label="Salary"
                      register={register}
                      errorMessage={fieldErrors.salary?.message}
                    />
                    <InputField
                      placeholder="Manager"
                      name="designation"
                      label="Designation"
                      register={register}
                      errorMessage={fieldErrors.designation?.message}
                    />
                    <InputField
                      placeholder="12345-6789012-3"
                      name="cnic"
                      label="CNIC"
                      register={register}
                      errorMessage={fieldErrors.cnic?.message}
                    />
                    {!isEditMode && (
                      <>
                        <SelectField
                          register={register}
                          name="department"
                          label="Department"
                          onChange={onDepartmentChange}
                          errorMessage={fieldErrors.department?.message}
                          options={DEPARTMENT_OPTIONS}
                        />

                        <SelectField
                          register={register}
                          name="type"
                          label="Role"
                          errorMessage={fieldErrors.type?.message}
                          options={getDepartmentRoles(selectedDepartment)}
                        />
                      </>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Address
                      </label>
                      <textarea
                        {...register("address")}
                        className="mt-1 p-3 w-full border rounded-md shadow-sm focus:ring-2 focus:ring-indigo-500"
                        placeholder="123 Main St, City"
                      />
                      {fieldErrors.address && (
                        <span className="text-sm text-red-500">
                          {fieldErrors.address.message}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contact Info Section */}
                <div className="space-y-6 mt-6">
                  <h3 className="text-xl font-semibold text-[#0f243f] flex items-center gap-2">
                    <DocumentTextIcon className="h-5 w-5" />
                    {isSupportDepartment(selectedDepartment)
                      ? "Contact Information"
                      : "Account Information"}
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    {!isEditMode && (
                      <>
                        <InputField
                          placeholder="<EMAIL>"
                          name="email"
                          label="Email"
                          register={register}
                          errorMessage={fieldErrors.email?.message}
                          type="email"
                        />
                        {!isSupportDepartment(selectedDepartment) && (
                          <InputField
                            placeholder="password"
                            type="password"
                            name="password"
                            label="Password"
                            register={register}
                            errorMessage={fieldErrors.password?.message}
                          />
                        )}
                      </>
                    )}

                    <InputField
                      placeholder="1234567890"
                      name="phone"
                      label="Phone Number"
                      register={register}
                      errorMessage={fieldErrors.phone?.message}
                      type="tel"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6"></div>
                </div>

                <div className="flex justify-center mt-6">
                  <Button
                    onClick={handleSubmit(onSubmit)}
                    pending={isFormSubmitting}
                  >
                    {isEditMode ? "Update Staff" : "Add Staff"}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </motion.div>

      <ResourceListingTable
        records={staffData?.items}
        totalRecords={staffData?.total}
        isDataLoading={isFetching}
        searchLabel="name"
        searchFieldValue={(record) => record.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Name", render: (s) => s.name },
          { label: "Designation", render: (s) => s.designation },
          { label: "Department", render: (s) => s.department },
          {
            label: "Role",
            render: (s) => TYPES.find((r) => r.value === s.type)?.label,
          },
          { label: "Email", render: (s) => s.email },
          { label: "Phone", render: (s) => s.phone },
        ]}
        onView={(item) => {
          setSelectedStaff(item);
          setIsViewModalOpen(true);
        }}
      />

      {isViewModalOpen && selectedStaff && (
        <ViewStaffModel
          staff={selectedStaff}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit();
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedStaff(null);
          }}
        />
      )}
    </ResourcePage>
  );
};

function isSupportDepartment(department: string) {
  return department === "SUPPORT";
}
