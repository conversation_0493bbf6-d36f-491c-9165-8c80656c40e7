import {
  addressSchema,
  cnicSchema,
  genderSchema,
  imageUrlSchema,
  nameSchema,
  passwordSchema,
  phoneNumberSchema,
  safePositiveNumberSchema,
} from "@/common/schemas/zod-common.schemas";
import { z } from "zod";

// ---------------------- Base-Staff-Schema ----------------------
const baseStaffSchema = z.object({
  name: nameSchema,
  email: z
    .string()
    .email({ message: "Invalid email address" })
    .max(100, { message: "Email must be less than 100 characters" }),
  phone: phoneNumberSchema,
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.optional().nullable(),
  password: passwordSchema.optional().nullable(),
  designation: z
    .string()
    .nonempty()
    .max(100, { message: "Designation must be less than 100 characters" }),
  department: z.enum(["ACADEMIC", "ADMINISTRATION", "SUPPORT"], {
    message:
      "Department must be one of 'Academic', 'Administration', or 'Support'.",
  }),
  type: z.enum(["TEACHER", "BRANCH_ADMIN", "ACCOUNTANT", "SUPPORT_STAFF"], {
    message: "Role must be one of 'Teacher', 'Branch Admin', or 'Accountant'.",
  }),
  salary: safePositiveNumberSchema,
  cnic: cnicSchema,
});

// ---------------------- Create-Staff-Schema ----------------------
export const createStaffSchema = baseStaffSchema
  .refine(
    (data) => {
      // Check if the role is valid for the department
      if (data.department === "ACADEMIC" && data.type !== "TEACHER") {
        return false;
      }

      if (
        data.department === "ADMINISTRATION" &&
        data.type !== "BRANCH_ADMIN" &&
        data.type !== "ACCOUNTANT"
      ) {
        return false;
      }

      if (data.department === "SUPPORT" && data.type !== "SUPPORT_STAFF") {
        return false;
      }

      return true;
    },
    {
      message: "Invalid role for the selected department.",
      path: ["type"],
    },
  )
  .refine(
    (data) => {
      if (data.department !== "SUPPORT" && !data.password) {
        return false;
      }
      return true;
    },
    {
      message: "Password is required for non-support staff.",
      path: ["password"],
    },
  );

// ---------------------- Update-Staff-Schema ----------------------
export const updateStaffSchema = baseStaffSchema
  .omit({
    department: true,
    type: true,
    email: true,
    password: true,
  })
  .partial();
