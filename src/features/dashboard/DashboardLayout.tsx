import { Outlet } from "react-router";
import Sidebar from "./components/Sidebar";
import { Breadcrumbs } from "@/common/components/BreadCrumbs";
import { useAcademicSessions } from "../academic-session/session-query";
import { useBranchStore } from "../onboarding/setup/branches/branches.store";
import { DashboardSkeleton } from "./components/DashboardSkeleton";
import { useSidebarStore } from "./components/sidebar/sidebar.store";

export function DashboardLayout() {
  useSidebarStore();
  const { selectedBranch } = useBranchStore();
  const { isFetching } = useAcademicSessions(selectedBranch?.id);
  return (
    <div className="flex flex-1">
      <div className="md:w-64 flex-shrink-0">
        <Sidebar />
      </div>
      <div
        className={`flex-1 p-4 overflow-auto bg-base-100 transition-all duration-300 ease-in-out`}
      >
        <div className="max-w-[1600px] mx-auto">
          {isFetching ? (
            <DashboardSkeleton />
          ) : (
            <>
              <Breadcrumbs />
              <Outlet />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
