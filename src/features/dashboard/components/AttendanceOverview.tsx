import { useMemo } from "react";
import { motion } from "framer-motion";
import { ClockIcon, CheckCircleIcon, XCircleIcon, ExclamationCircleIcon } from "@heroicons/react/24/outline";
import type { Class } from "@/features/classes/types/class.type";

interface AttendanceOverviewProps {
  classes: Class[];
  isLoading?: boolean;
}

export const AttendanceOverview = ({ classes, isLoading }: AttendanceOverviewProps) => {
  // Mock attendance data - in real implementation, this would come from attendance API
  const attendanceData = useMemo(() => {
    const today = new Date().toISOString().split('T')[0];
    
    // Generate mock attendance data for demonstration
    return classes.map(cls => {
      const sections = cls.sections.map(section => {
        // Mock data - replace with real attendance data
        const totalStudents = Math.floor(Math.random() * 30) + 20; // 20-50 students
        const presentStudents = Math.floor(totalStudents * (0.85 + Math.random() * 0.1)); // 85-95% attendance
        const lateStudents = Math.floor((totalStudents - presentStudents) * 0.3);
        const absentStudents = totalStudents - presentStudents - lateStudents;
        
        return {
          id: section.id,
          name: section.name,
          teacher: section.classTeacher.name,
          totalStudents,
          presentStudents,
          lateStudents,
          absentStudents,
          attendanceRate: (presentStudents / totalStudents) * 100,
        };
      });
      
      const totalStudents = sections.reduce((sum, s) => sum + s.totalStudents, 0);
      const totalPresent = sections.reduce((sum, s) => sum + s.presentStudents, 0);
      const totalLate = sections.reduce((sum, s) => sum + s.lateStudents, 0);
      const totalAbsent = sections.reduce((sum, s) => sum + s.absentStudents, 0);
      
      return {
        id: cls.id,
        name: cls.name,
        sections,
        totalStudents,
        totalPresent,
        totalLate,
        totalAbsent,
        attendanceRate: totalStudents > 0 ? (totalPresent / totalStudents) * 100 : 0,
      };
    });
  }, [classes]);

  const overallStats = useMemo(() => {
    const totalStudents = attendanceData.reduce((sum, c) => sum + c.totalStudents, 0);
    const totalPresent = attendanceData.reduce((sum, c) => sum + c.totalPresent, 0);
    const totalLate = attendanceData.reduce((sum, c) => sum + c.totalLate, 0);
    const totalAbsent = attendanceData.reduce((sum, c) => sum + c.totalAbsent, 0);
    const overallRate = totalStudents > 0 ? (totalPresent / totalStudents) * 100 : 0;
    
    return {
      totalStudents,
      totalPresent,
      totalLate,
      totalAbsent,
      overallRate,
    };
  }, [attendanceData]);

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="skeleton h-32 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="card-title text-xl font-semibold flex items-center">
            <ClockIcon className="w-6 h-6 mr-2 text-primary" />
            Today's Attendance Overview
          </h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-primary">
              {overallStats.overallRate.toFixed(1)}%
            </div>
            <div className="text-xs text-base-content/60">Overall Attendance</div>
          </div>
        </div>

        {/* Overall Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <motion.div
            className="text-center p-4 bg-success/5 rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <CheckCircleIcon className="w-8 h-8 mx-auto text-success mb-2" />
            <div className="text-2xl font-bold text-success">{overallStats.totalPresent}</div>
            <div className="text-xs text-base-content/60">Present</div>
          </motion.div>

          <motion.div
            className="text-center p-4 bg-warning/5 rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <ExclamationCircleIcon className="w-8 h-8 mx-auto text-warning mb-2" />
            <div className="text-2xl font-bold text-warning">{overallStats.totalLate}</div>
            <div className="text-xs text-base-content/60">Late</div>
          </motion.div>

          <motion.div
            className="text-center p-4 bg-error/5 rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <XCircleIcon className="w-8 h-8 mx-auto text-error mb-2" />
            <div className="text-2xl font-bold text-error">{overallStats.totalAbsent}</div>
            <div className="text-xs text-base-content/60">Absent</div>
          </motion.div>

          <motion.div
            className="text-center p-4 bg-primary/5 rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <ClockIcon className="w-8 h-8 mx-auto text-primary mb-2" />
            <div className="text-2xl font-bold text-primary">{overallStats.totalStudents}</div>
            <div className="text-xs text-base-content/60">Total</div>
          </motion.div>
        </div>

        {/* Class-wise Attendance */}
        <div className="space-y-4">
          <h4 className="font-medium text-base-content/80">Class-wise Breakdown</h4>
          
          {attendanceData.length === 0 ? (
            <div className="text-center py-8">
              <ClockIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
              <p className="text-base-content/60">No attendance data available</p>
            </div>
          ) : (
            <div className="space-y-3">
              {attendanceData.map((classData, index) => (
                <motion.div
                  key={classData.id}
                  className="p-4 border border-base-300 rounded-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-base-content">{classData.name}</h5>
                    <div className="text-right">
                      <div className={`text-lg font-semibold ${
                        classData.attendanceRate >= 90 ? 'text-success' :
                        classData.attendanceRate >= 80 ? 'text-warning' :
                        'text-error'
                      }`}>
                        {classData.attendanceRate.toFixed(1)}%
                      </div>
                      <div className="text-xs text-base-content/60">
                        {classData.totalPresent}/{classData.totalStudents}
                      </div>
                    </div>
                  </div>

                  {/* Progress bar */}
                  <div className="w-full bg-base-300 rounded-full h-2 mb-3">
                    <div 
                      className={`h-2 rounded-full ${
                        classData.attendanceRate >= 90 ? 'bg-success' :
                        classData.attendanceRate >= 80 ? 'bg-warning' :
                        'bg-error'
                      }`}
                      style={{ width: `${classData.attendanceRate}%` }}
                    />
                  </div>

                  {/* Sections breakdown */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {classData.sections.map((section) => (
                      <div key={section.id} className="p-2 bg-base-200/50 rounded text-sm">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Section {section.name}</span>
                          <span className={`font-semibold ${
                            section.attendanceRate >= 90 ? 'text-success' :
                            section.attendanceRate >= 80 ? 'text-warning' :
                            'text-error'
                          }`}>
                            {section.attendanceRate.toFixed(0)}%
                          </span>
                        </div>
                        <div className="text-xs text-base-content/60">
                          P: {section.presentStudents} | L: {section.lateStudents} | A: {section.absentStudents}
                        </div>
                        <div className="text-xs text-base-content/60 truncate">
                          Teacher: {section.teacher}
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Action */}
        <div className="mt-6 pt-4 border-t border-base-300">
          <a 
            href="/student-attendance" 
            className="btn btn-primary w-full"
          >
            <ClockIcon className="w-4 h-4 mr-2" />
            Take Attendance
          </a>
        </div>
      </div>
    </div>
  );
};
