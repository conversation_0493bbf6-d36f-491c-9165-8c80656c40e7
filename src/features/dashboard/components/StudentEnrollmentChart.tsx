import { useMemo } from "react";
import { motion } from "framer-motion";
import {
  AcademicCapIcon,
  ArrowTrendingUpIcon,
} from "@heroicons/react/24/outline";
import type { StudentEnrollment } from "@/features/students/types/students.type";

interface StudentEnrollmentChartProps {
  enrollments: StudentEnrollment[];
  isLoading?: boolean;
}

interface MonthlyEnrollmentData {
  month: string;
  admissions: number;
  transfers: number;
  total: number;
}

export const StudentEnrollmentChart = ({
  enrollments,
  isLoading,
}: StudentEnrollmentChartProps) => {
  const chartData = useMemo(() => {
    const now = new Date();
    const monthsData: Record<string, MonthlyEnrollmentData> = {};

    // Initialize last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = date.toLocaleDateString("en-US", {
        month: "short",
        year: "2-digit",
      });
      monthsData[monthKey] = {
        month: monthKey,
        admissions: 0,
        transfers: 0,
        total: 0,
      };
    }

    // Process enrollments
    enrollments.forEach((enrollment) => {
      const enrollmentDate = new Date(enrollment.date);
      const monthKey = enrollmentDate.toLocaleDateString("en-US", {
        month: "short",
        year: "2-digit",
      });

      if (monthsData[monthKey]) {
        monthsData[monthKey].total++;
        if (enrollment.type === "ADMISSION") {
          monthsData[monthKey].admissions++;
        } else if (enrollment.type === "TRANSFER_IN") {
          monthsData[monthKey].transfers++;
        }
      }
    });

    return Object.values(monthsData);
  }, [enrollments]);

  const maxValue = Math.max(...chartData.map((d) => d.total), 1);
  const totalEnrollments = enrollments.length;
  const activeStudents = enrollments.filter(
    (e) => e.status === "ACTIVE"
  ).length;
  const growthRate =
    chartData.length > 1
      ? ((chartData[chartData.length - 1]?.total ??
          0 - (chartData[chartData.length - 2]?.total ?? 0)) /
          Math.max(chartData[chartData.length - 2]?.total ?? 1, 1)) *
        100
      : 0;

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="skeleton h-64 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="card-title text-xl font-semibold flex items-center">
            <AcademicCapIcon className="w-6 h-6 mr-2 text-primary" />
            Student Enrollment Trends
          </h3>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">
                {activeStudents}
              </div>
              <div className="text-xs text-base-content/60">
                Active Students
              </div>
            </div>
            <div className="text-right">
              <div
                className={`text-lg font-semibold flex items-center ${
                  growthRate >= 0 ? "text-success" : "text-error"
                }`}
              >
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                {growthRate >= 0 ? "+" : ""}
                {growthRate.toFixed(1)}%
              </div>
              <div className="text-xs text-base-content/60">Growth Rate</div>
            </div>
          </div>
        </div>

        {/* Chart */}
        <div className="relative">
          <div className="flex items-end justify-between h-64 mb-4">
            {chartData.map((data, index) => {
              const admissionsHeight = (data.admissions / maxValue) * 100;
              const transfersHeight = (data.transfers / maxValue) * 100;

              return (
                <motion.div
                  key={index}
                  className="flex flex-col items-center flex-1 mx-1"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="relative w-full max-w-16 h-48 flex flex-col justify-end">
                    {/* Transfers */}
                    {data.transfers > 0 && (
                      <motion.div
                        className="w-full bg-secondary/70 rounded-t transition-all duration-500 ease-out"
                        style={{ height: `${transfersHeight}%` }}
                        title={`${data.transfers} transfers`}
                        initial={{ height: 0 }}
                        animate={{ height: `${transfersHeight}%` }}
                        transition={{ duration: 0.8, delay: index * 0.1 }}
                      />
                    )}
                    {/* Admissions */}
                    {data.admissions > 0 && (
                      <motion.div
                        className="w-full bg-primary rounded-b transition-all duration-500 ease-out"
                        style={{ height: `${admissionsHeight}%` }}
                        title={`${data.admissions} admissions`}
                        initial={{ height: 0 }}
                        animate={{ height: `${admissionsHeight}%` }}
                        transition={{ duration: 0.8, delay: index * 0.1 + 0.2 }}
                      />
                    )}
                    {/* Total count label */}
                    {data.total > 0 && (
                      <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs font-medium text-base-content">
                        {data.total}
                      </div>
                    )}
                  </div>
                  {/* Month label */}
                  <div className="text-xs text-base-content/70 mt-2 font-medium">
                    {data.month}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 h-48 flex flex-col justify-between text-xs text-base-content/60">
            {Array.from({ length: 5 }, (_, i) => (
              <span key={i}>{Math.round((maxValue * (4 - i)) / 4)}</span>
            ))}
          </div>
        </div>

        {/* Legend and Summary */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-primary rounded mr-2"></div>
              <span>New Admissions</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-secondary/70 rounded mr-2"></div>
              <span>Transfers</span>
            </div>
          </div>

          <div className="text-sm text-base-content/70">
            Total Enrollments:{" "}
            <span className="font-semibold">{totalEnrollments}</span>
          </div>
        </div>

        {/* Enrollment Status Breakdown */}
        <div className="mt-6 pt-4 border-t border-base-300">
          <h4 className="font-medium text-base-content/80 mb-3">
            Enrollment Status
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(["ACTIVE", "GRADUATED", "WITHDRAWN", "EXPELLED"] as const).map(
              (status) => {
                const count = enrollments.filter(
                  (e) => e.status === status
                ).length;
                const percentage =
                  totalEnrollments > 0 ? (count / totalEnrollments) * 100 : 0;

                const statusColors = {
                  ACTIVE: "text-success",
                  GRADUATED: "text-primary",
                  WITHDRAWN: "text-warning",
                  EXPELLED: "text-error",
                };

                return (
                  <div key={status} className="text-center">
                    <div
                      className={`text-lg font-semibold ${statusColors[status]}`}
                    >
                      {count}
                    </div>
                    <div className="text-xs text-base-content/60 capitalize">
                      {status.toLowerCase()}
                    </div>
                    <div className="text-xs text-base-content/50">
                      {percentage.toFixed(1)}%
                    </div>
                  </div>
                );
              }
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
