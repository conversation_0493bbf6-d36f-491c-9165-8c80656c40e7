import { useMemo } from "react";
import { motion } from "framer-motion";
import { UserGroupIcon, AcademicCapIcon, CogIcon, WrenchScrewdriverIcon } from "@heroicons/react/24/outline";
import type { Staff } from "@/features/staff/types/staff.type";

interface StaffDistributionChartProps {
  staff: Staff[];
  isLoading?: boolean;
}

export const StaffDistributionChart = ({ staff, isLoading }: StaffDistributionChartProps) => {
  const distributionData = useMemo(() => {
    const departments = staff.reduce((acc, member) => {
      acc[member.department] = (acc[member.department] ?? 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const total = staff.length;
    
    return [
      {
        department: 'ACADEMIC',
        count: departments.ACADEMIC ?? 0,
        percentage: total > 0 ? ((departments.ACADEMIC ?? 0) / total) * 100 : 0,
        color: 'bg-primary',
        icon: <AcademicCapIcon className="w-5 h-5" />,
        label: 'Academic',
      },
      {
        department: 'ADMINISTRATION',
        count: departments.ADMINISTRATION ?? 0,
        percentage: total > 0 ? ((departments.ADMINISTRATION ?? 0) / total) * 100 : 0,
        color: 'bg-secondary',
        icon: <CogIcon className="w-5 h-5" />,
        label: 'Administration',
      },
      {
        department: 'SUPPORT',
        count: departments.SUPPORT ?? 0,
        percentage: total > 0 ? ((departments.SUPPORT ?? 0) / total) * 100 : 0,
        color: 'bg-accent',
        icon: <WrenchScrewdriverIcon className="w-5 h-5" />,
        label: 'Support',
      },
    ];
  }, [staff]);

  const totalStaff = staff.length;

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="skeleton h-48 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-6 flex items-center">
          <UserGroupIcon className="w-5 h-5 mr-2 text-primary" />
          Staff Distribution
          <span className="badge badge-primary badge-sm ml-2">{totalStaff}</span>
        </h3>

        {totalStaff === 0 ? (
          <div className="text-center py-8">
            <UserGroupIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
            <p className="text-base-content/60">No staff members found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Donut Chart Representation */}
            <div className="relative flex justify-center mb-6">
              <div className="relative w-32 h-32">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-base-300"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="transparent"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  {distributionData.map((dept, index) => {
                    const prevPercentages = distributionData
                      .slice(0, index)
                      .reduce((sum, d) => sum + d.percentage, 0);
                    
                    const strokeDasharray = `${dept.percentage} ${100 - dept.percentage}`;
                    const strokeDashoffset = -prevPercentages;
                    
                    const colors = {
                      'bg-primary': 'hsl(var(--p))',
                      'bg-secondary': 'hsl(var(--s))',
                      'bg-accent': 'hsl(var(--a))',
                    };

                    return dept.count > 0 ? (
                      <motion.path
                        key={dept.department}
                        stroke={colors[dept.color as keyof typeof colors]}
                        strokeWidth="3"
                        fill="transparent"
                        strokeDasharray={strokeDasharray}
                        strokeDashoffset={strokeDashoffset}
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        initial={{ strokeDasharray: "0 100" }}
                        animate={{ strokeDasharray }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    ) : null;
                  })}
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-lg font-bold text-base-content">{totalStaff}</div>
                    <div className="text-xs text-base-content/60">Total</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Department Breakdown */}
            <div className="space-y-3">
              {distributionData.map((dept, index) => (
                <motion.div
                  key={dept.department}
                  className="flex items-center justify-between p-3 rounded-lg bg-base-200/50"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${dept.color}/10 text-${dept.color.split('-')[1]}`}>
                      {dept.icon}
                    </div>
                    <div>
                      <div className="font-medium text-base-content">{dept.label}</div>
                      <div className="text-sm text-base-content/70">
                        {dept.count} member{dept.count !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-base-content">
                      {dept.percentage.toFixed(1)}%
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Staff Types Breakdown */}
            <div className="mt-6 pt-4 border-t border-base-300">
              <h4 className="font-medium text-base-content/80 mb-3">Staff Roles</h4>
              <div className="grid grid-cols-2 gap-3">
                {(['TEACHER', 'BRANCH_ADMIN', 'ACCOUNTANT', 'SUPPORT_STAFF'] as const).map((type) => {
                  const count = staff.filter(s => s.type === type).length;
                  const typeLabels = {
                    TEACHER: 'Teachers',
                    BRANCH_ADMIN: 'Admins',
                    ACCOUNTANT: 'Accountants',
                    SUPPORT_STAFF: 'Support',
                  };

                  return (
                    <div key={type} className="text-center p-2 bg-base-200/30 rounded">
                      <div className="text-sm font-semibold text-base-content">
                        {count}
                      </div>
                      <div className="text-xs text-base-content/60">
                        {typeLabels[type]}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
