import blue from "@/assets/blue.png";
import { Avatar } from "@/common/components/ui/Avatar";
import { Link, useNavigate } from "react-router";
import { SelectBranch } from "./components/SelectBranch";
import { SelectAcademicSession } from "./components/SelectAcademicSession";
import { Bars3Icon } from "@heroicons/react/24/outline";
import { useSidebarStore } from "../sidebar/sidebar.store";
import { useUserProfile } from "@/core/user/user-query";
import { isInstituteOwner } from "@/utils/user-roles.utils";
import { FaCog, FaSignOutAlt, FaUser } from "react-icons/fa";
import { useAuthStore } from "@/core/auth/auth.store";

export const Header = () => {
  const { toggle } = useSidebarStore();
  const { data: user } = useUserProfile();
  const { clearAuth } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    clearAuth();
    await navigate("/login");
  };

  return (
    <nav className="bg-base-100 shadow-md px-6 py-4 fixed top-0 left-0 w-full z-50">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          {/* Mobile menu toggle */}
          <button
            onClick={toggle}
            className="md:hidden p-1 rounded-lg text-primary hover:bg-base-200 transition-colors"
            aria-label="Toggle sidebar"
          >
            <Bars3Icon className="w-6 h-6" />
          </button>

          <Link to="/" className="items-center gap-3 hidden md:flex">
            <img src={blue} alt="Logo" className="h-10 w-auto" />
            <h1 className="text-2xl font-bold hover:text-info">E-Bridge</h1>
          </Link>
        </div>

        <div className="flex gap-6">
          <SelectAcademicSession />

          {isInstituteOwner(user) && <SelectBranch />}

          {/* ---------- User Profile --------------- */}
          <div className="dropdown dropdown-bottom dropdown-end">
            <div role="button" tabIndex={0} className="cursor-pointer">
              <Avatar />
            </div>
            <ul
              tabIndex={0}
              className="dropdown-content menu shadow-lg bg-base-100 ring-1 ring-black/5 focus:outline-none w-52  mt-3"
            >
              <li>
                <a className="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-base-200">
                  <FaUser className="text-primary" />
                  <span>Profile</span>
                </a>
              </li>
              <li>
                <a className="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-base-200">
                  <FaCog className="text-primary" />
                  <span>Settings</span>
                </a>
              </li>
              <li>
                <button
                  className="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-red-100 text-red-600"
                  onClick={handleLogout}
                >
                  <FaSignOutAlt className="text-red-500" />
                  <span>Logout</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
  );
};
