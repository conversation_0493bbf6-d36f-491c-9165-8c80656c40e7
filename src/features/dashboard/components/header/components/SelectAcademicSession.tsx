import { useAcademicSessions } from "@/features/academic-session/session-query";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { useEffect } from "react";

export const SelectAcademicSession = () => {
  const { selectedBranch } = useBranchStore();

  const { data: academicSessions, isFetching } = useAcademicSessions(
    selectedBranch?.id,
  );
  const { activeAcademicSession, setAcademicSession } =
    useAcademicSessionStore();

  const handleSessionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sessionId = e.target.value;
    const session = academicSessions?.find(
      (session) => session.id === sessionId,
    );
    if (session) {
      setAcademicSession(session);
    }
  };

  useEffect(() => {
    if (
      !activeAcademicSession &&
      academicSessions &&
      academicSessions.length > 0
    ) {
      const activeSession = academicSessions.find(
        (session) => session.isActive,
      );
      if (activeSession) {
        setAcademicSession(activeSession);
      }
    }
  }, [academicSessions, activeAcademicSession, setAcademicSession]);

  return (
    <div>
      <select
        className="select select-primary w-full"
        value={selectedBranch?.id ?? ""}
        onChange={handleSessionChange}
        disabled={isFetching || !academicSessions?.length}
      >
        <option value="" className="text-center" disabled>
          {isFetching
            ? "Loading sessions..."
            : !academicSessions?.length
              ? "No sessions found"
              : "Select a session"}
        </option>
        {academicSessions?.map((session) => (
          <option key={session.id} value={session.id}>
            {session.name}
          </option>
        ))}
      </select>
    </div>
  );
};
