import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { useBranches } from "@/features/onboarding/setup/branches/branches-query";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { useInstitute } from "@/features/onboarding/setup/Institute/institute-query";
import { useEffect } from "react";

export const SelectBranch = () => {
  const { data: instituteData } = useInstitute();
  const { data: branches, isFetching } = useBranches(
    instituteData?.institute?.id,
  );
  const { selectedBranch, setSelectedBranch } = useBranchStore();
  const { clearAcademicSession } = useAcademicSessionStore();

  const handleBranchChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const branchId = e.target.value;
    const branch = branches?.find((branch) => branch.id === branchId);
    if (branch) {
      setSelectedBranch(branch);
      clearAcademicSession();
    }
  };

  useEffect(() => {
    function selectMainBranchIfExists() {
      if (!branches || branches.length === 0) return;
      const mainBranch = branches.find((branch) => branch.isMain);
      if (mainBranch) {
        setSelectedBranch(mainBranch);
      }
    }

    if (!selectedBranch) selectMainBranchIfExists();
  });

  return (
    <div>
      <select
        className="select select-primary w-full"
        value={selectedBranch?.id ?? ""}
        onChange={handleBranchChange}
        disabled={isFetching || !branches}
      >
        <option value="" className="text-center" disabled>
          {isFetching ? "Loading branches..." : "Select a Branch"}
        </option>
        {branches?.map((branch) => (
          <option key={branch.id} value={branch.id}>
            {branch.name}
          </option>
        ))}
      </select>
    </div>
  );
};
