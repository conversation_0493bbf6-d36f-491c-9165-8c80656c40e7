import { useMemo } from "react";
import { motion } from "framer-motion";
import { BuildingLibraryIcon, UserGroupIcon, AcademicCapIcon } from "@heroicons/react/24/outline";
import type { Class } from "@/features/classes/types/class.type";
import type { StudentEnrollment } from "@/features/students/types/students.type";

interface ClassPerformanceOverviewProps {
  classes: Class[];
  enrollments: StudentEnrollment[];
  isLoading?: boolean;
}

interface ClassData {
  id: string;
  name: string;
  totalSections: number;
  totalStudents: number;
  averageStudentsPerSection: number;
  sections: {
    id: string;
    name: string;
    studentCount: number;
    teacher: string;
  }[];
}

export const ClassPerformanceOverview = ({ 
  classes, 
  enrollments, 
  isLoading 
}: ClassPerformanceOverviewProps) => {
  const classData = useMemo(() => {
    return classes.map(cls => {
      const classEnrollments = enrollments.filter(
        e => e.classSection.class.id === cls.id && e.status === "ACTIVE"
      );

      const sections = cls.sections.map(section => {
        const sectionStudents = classEnrollments.filter(
          e => e.classSection.id === section.id
        );
        
        return {
          id: section.id,
          name: section.name,
          studentCount: sectionStudents.length,
          teacher: section.classTeacher.name,
        };
      });

      const totalStudents = classEnrollments.length;
      const averageStudentsPerSection = sections.length > 0 ? totalStudents / sections.length : 0;

      return {
        id: cls.id,
        name: cls.name,
        totalSections: cls.sections.length,
        totalStudents,
        averageStudentsPerSection,
        sections,
      };
    }).sort((a, b) => b.totalStudents - a.totalStudents);
  }, [classes, enrollments]);

  const totalStudents = enrollments.filter(e => e.status === "ACTIVE").length;
  const maxStudentsInClass = Math.max(...classData.map(c => c.totalStudents), 1);

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="skeleton h-64 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="card-title text-xl font-semibold flex items-center">
            <BuildingLibraryIcon className="w-6 h-6 mr-2 text-primary" />
            Class Performance Overview
          </h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-primary">{totalStudents}</div>
            <div className="text-xs text-base-content/60">Total Students</div>
          </div>
        </div>

        {classData.length === 0 ? (
          <div className="text-center py-8">
            <BuildingLibraryIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
            <p className="text-base-content/60">No classes found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Class Distribution Chart */}
            <div className="space-y-3">
              {classData.map((cls, index) => {
                const percentage = maxStudentsInClass > 0 ? (cls.totalStudents / maxStudentsInClass) * 100 : 0;
                
                return (
                  <motion.div
                    key={cls.id}
                    className="space-y-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="font-medium text-base-content">{cls.name}</div>
                        <span className="badge badge-outline badge-sm">
                          {cls.totalSections} section{cls.totalSections !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-base-content">{cls.totalStudents}</div>
                        <div className="text-xs text-base-content/60">students</div>
                      </div>
                    </div>
                    
                    {/* Progress bar */}
                    <div className="w-full bg-base-300 rounded-full h-2">
                      <motion.div 
                        className="bg-primary h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${percentage}%` }}
                        transition={{ duration: 0.8, delay: index * 0.1 }}
                      />
                    </div>
                    
                    {/* Section breakdown */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-2">
                      {cls.sections.map((section) => (
                        <div 
                          key={section.id}
                          className="p-2 bg-base-200/50 rounded text-sm"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">Section {section.name}</span>
                            <span className="text-primary font-semibold">{section.studentCount}</span>
                          </div>
                          <div className="text-xs text-base-content/60 truncate">
                            Teacher: {section.teacher}
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Summary Statistics */}
            <div className="mt-6 pt-4 border-t border-base-300">
              <h4 className="font-medium text-base-content/80 mb-3">Class Statistics</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-primary/5 rounded-lg">
                  <div className="text-lg font-semibold text-primary">
                    {classData.length}
                  </div>
                  <div className="text-xs text-base-content/60">Total Classes</div>
                </div>
                
                <div className="text-center p-3 bg-secondary/5 rounded-lg">
                  <div className="text-lg font-semibold text-secondary">
                    {classData.reduce((sum, c) => sum + c.totalSections, 0)}
                  </div>
                  <div className="text-xs text-base-content/60">Total Sections</div>
                </div>
                
                <div className="text-center p-3 bg-accent/5 rounded-lg">
                  <div className="text-lg font-semibold text-accent">
                    {classData.length > 0 ? 
                      Math.round(totalStudents / classData.length) : 0}
                  </div>
                  <div className="text-xs text-base-content/60">Avg per Class</div>
                </div>
                
                <div className="text-center p-3 bg-success/5 rounded-lg">
                  <div className="text-lg font-semibold text-success">
                    {classData.reduce((sum, c) => sum + c.totalSections, 0) > 0 ?
                      Math.round(totalStudents / classData.reduce((sum, c) => sum + c.totalSections, 0)) : 0}
                  </div>
                  <div className="text-xs text-base-content/60">Avg per Section</div>
                </div>
              </div>
            </div>

            {/* Top Performing Classes */}
            <div className="mt-6 pt-4 border-t border-base-300">
              <h4 className="font-medium text-base-content/80 mb-3">Enrollment Leaders</h4>
              <div className="space-y-2">
                {classData.slice(0, 3).map((cls, index) => {
                  const medals = ['🥇', '🥈', '🥉'];
                  return (
                    <div key={cls.id} className="flex items-center justify-between p-2 bg-base-200/30 rounded">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{medals[index]}</span>
                        <div>
                          <div className="font-medium text-base-content">{cls.name}</div>
                          <div className="text-xs text-base-content/60">
                            {cls.totalSections} sections • Avg: {cls.averageStudentsPerSection.toFixed(1)} students/section
                          </div>
                        </div>
                      </div>
                      <div className="text-lg font-semibold text-primary">
                        {cls.totalStudents}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
