import { useMemo } from "react";
import { motion } from "framer-motion";
import { 
  ClockIcon, 
  AcademicCapIcon, 
  DocumentCheckIcon,
  UserPlusIcon,
  ArrowRightIcon 
} from "@heroicons/react/24/outline";
import type { StudentEnrollment } from "@/features/students/types/students.type";
import type { Exam } from "@/features/exams/create-exams/types/exam.type";
import { formatDateInLocalFormat } from "@/utils/utils";

interface RecentActivitiesProps {
  enrollments: StudentEnrollment[];
  exams: Exam[];
  isLoading?: boolean;
}

interface Activity {
  id: string;
  type: 'enrollment' | 'exam';
  title: string;
  description: string;
  date: string;
  icon: React.ReactNode;
  color: string;
}

export const RecentActivities = ({ enrollments, exams, isLoading }: RecentActivitiesProps) => {
  const activities = useMemo(() => {
    const allActivities: Activity[] = [];

    // Add recent enrollments
    enrollments
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .forEach(enrollment => {
        allActivities.push({
          id: `enrollment-${enrollment.id}`,
          type: 'enrollment',
          title: 'New Student Enrollment',
          description: `${enrollment.student.name} enrolled in ${enrollment.classSection.class.name} - ${enrollment.classSection.name}`,
          date: enrollment.createdAt,
          icon: <UserPlusIcon className="w-4 h-4" />,
          color: 'text-success',
        });
      });

    // Add recent exams
    exams
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .forEach(exam => {
        allActivities.push({
          id: `exam-${exam.id}`,
          type: 'exam',
          title: 'Exam Created',
          description: `${exam.name} scheduled from ${formatDateInLocalFormat(exam.startDate)} to ${formatDateInLocalFormat(exam.endDate)}`,
          date: exam.createdAt,
          icon: <DocumentCheckIcon className="w-4 h-4" />,
          color: 'text-primary',
        });
      });

    // Sort all activities by date and take the most recent 8
    return allActivities
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 8);
  }, [enrollments, exams]);

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-32 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="skeleton h-16 w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-4 flex items-center">
          <ClockIcon className="w-5 h-5 mr-2 text-primary" />
          Recent Activities
          <span className="badge badge-primary badge-sm ml-2">{activities.length}</span>
        </h3>

        {activities.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="w-12 h-12 mx-auto text-base-content/30 mb-3" />
            <p className="text-base-content/60">No recent activities</p>
          </div>
        ) : (
          <div className="space-y-3">
            {activities.map((activity, index) => (
              <motion.div
                key={activity.id}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-base-200/50 transition-colors cursor-pointer"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className={`p-2 rounded-lg ${activity.color === 'text-success' ? 'bg-success/10' : 'bg-primary/10'} ${activity.color} flex-shrink-0`}>
                  {activity.icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-base-content truncate">
                      {activity.title}
                    </h4>
                    <span className="text-xs text-base-content/60 flex-shrink-0 ml-2">
                      {getTimeAgo(activity.date)}
                    </span>
                  </div>
                  <p className="text-xs text-base-content/70 mt-1 line-clamp-2">
                    {activity.description}
                  </p>
                </div>
                
                <ArrowRightIcon className="w-4 h-4 text-base-content/30 flex-shrink-0" />
              </motion.div>
            ))}
          </div>
        )}

        {/* Activity Summary */}
        <div className="mt-6 pt-4 border-t border-base-300">
          <h4 className="font-medium text-base-content/80 mb-3">Activity Summary</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-success/5 rounded-lg">
              <div className="text-lg font-bold text-success">
                {activities.filter(a => a.type === 'enrollment').length}
              </div>
              <div className="text-xs text-base-content/60">New Enrollments</div>
            </div>
            
            <div className="text-center p-3 bg-primary/5 rounded-lg">
              <div className="text-lg font-bold text-primary">
                {activities.filter(a => a.type === 'exam').length}
              </div>
              <div className="text-xs text-base-content/60">Exams Created</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 pt-4 border-t border-base-300">
          <div className="grid grid-cols-1 gap-2">
            <a 
              href="/students/admission" 
              className="btn btn-sm btn-outline btn-success w-full"
            >
              <UserPlusIcon className="w-4 h-4 mr-2" />
              Add New Student
            </a>
            <a 
              href="/exams" 
              className="btn btn-sm btn-outline btn-primary w-full"
            >
              <DocumentCheckIcon className="w-4 h-4 mr-2" />
              Create Exam
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
