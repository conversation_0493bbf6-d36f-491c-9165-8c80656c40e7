import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createInstitute,
  getOwnerInstitute,
  updateInstitute,
} from "./institute.service";
import { notify } from "@/lib/notify";

const getUseInstituteQueryKey = () => ["institutes/me"];

export const useInstitute = (enabled = true) => {
  return useQuery({
    queryKey: getUseInstituteQueryKey(),
    queryFn: getOwnerInstitute,
    enabled,
    staleTime: Infinity,
  });
};

export const useCreateInstitute = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createInstitute,
    onError: () => {
      notify.error("Failed to create institute");
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getUseInstituteQueryKey(),
      });
      notify.success("Institute created successfully");
    },
  });
};

export const useUpdateInstitute = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateInstitute,
    onError: () => {
      notify.error("Failed to update institute");
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getUseInstituteQueryKey(),
      });
      notify.success("Institute updated successfully");
    },
  });
};
