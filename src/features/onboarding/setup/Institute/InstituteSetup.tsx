/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, type ChangeEvent, useEffect } from "react";
import { InputField } from "@/common/components/ui/form/InputField";
import { motion } from "framer-motion";
import {
  PhotoIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  CheckCircleIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import { logger } from "@/lib/logger";
import { uploadFile } from "@/common/services/upload.service";
import { type SubmitHandler, useForm } from "react-hook-form";
import type { CreateInstitutePayload } from "./institute.type";
import { zodResolver } from "@hookform/resolvers/zod";
import { createInstituteSchema } from "./institute.schema";
import { showErrorNotification } from "@/utils/exception.utils";
import {
  useCreateInstitute,
  useInstitute,
  useUpdateInstitute,
} from "./institute-query";

interface SetUpInstituteProps {
  setNextEnable: (enable: boolean) => void;
}

const SetupInstitute = ({ setNextEnable }: SetUpInstituteProps) => {
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);

  const createInstituteMutation = useCreateInstitute();
  const updateInstituteMutation = useUpdateInstitute();
  console.log("In step 1");
  const { data: instituteData, isFetching: isInstituteLoading } =
    useInstitute();

  const {
    register,
    handleSubmit,
    formState: { errors: formErrors },
  } = useForm<CreateInstitutePayload>({
    resolver: zodResolver(createInstituteSchema),
    defaultValues: {
      name: "",
      email: "",
    },
  });

  const uploadInstituteLogo = async (logo: File) => {
    try {
      setIsUploadingLogo(true);
      const { url } = await uploadFile(logo, "image");
      return url;
    } catch (error) {
      logger.error("Error uploading institute logo:", error);
      throw error;
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const onSubmit: SubmitHandler<CreateInstitutePayload> = async (data) => {
    try {
      if (logoFile) {
        const logoUrl = await uploadInstituteLogo(logoFile);
        data.logo = logoUrl;
      }
      await createInstituteMutation.mutateAsync(data);
    } catch (error) {
      logger.error(error);
    }
  };

  async function handleLogoChange(e: ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) {
      logger.error("No file selected for institute logo upload");
      return;
    }
    setLogoFile(file);

    // if institute doesn't exist, don't upload logo
    if (!instituteData?.institute) return;

    // if institute already has a logo, don't upload
    if (!instituteData.institute.logo) return;

    try {
      setIsUploadingLogo(true);
      const logoUrl = await uploadInstituteLogo(file);
      await updateInstituteMutation.mutateAsync({
        payload: { logo: logoUrl },
      });
    } catch (error) {
      showErrorNotification(error);
    } finally {
      setIsUploadingLogo(false);
    }
  }

  useEffect(() => {
    setNextEnable(Boolean(instituteData?.institute));
  }, [instituteData, setNextEnable]);

  if (!instituteData) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col items-center mb-4">
        <h2 className="text-2xl font-bold">Institute Details</h2>
        <p className="text-base-content/70 mt-2 text-center">
          Tell us about your educational institution
        </p>
      </div>

      <div className="flex flex-col items-center mb-8">
        <div className="avatar">
          <div className="w-32 h-32 rounded-full ring-2 ring-primary ring-offset-base-100 ring-offset-2 flex items-center justify-center bg-base-200 overflow-hidden">
            {(instituteData.institute?.logo ?? logoFile) ? (
              <img
                src={
                  logoFile
                    ? URL.createObjectURL(logoFile)
                    : instituteData.institute?.logo
                }
                alt="Institute logo"
                className="w-full h-full object-cover"
              />
            ) : (
              <PhotoIcon className="h-16 w-16 text-base-content/30" />
            )}
          </div>
        </div>

        <div className="mt-4">
          <label className={`btn btn-sm  ? "btn-disabled" : "btn-outline"}`}>
            {isUploadingLogo ? (
              <>
                <span className="loading loading-spinner loading-sm mr-2"></span>
                Uploading...
              </>
            ) : (
              "Upload Logo"
            )}
            <input
              type="file"
              className="hidden"
              accept="image/*"
              onChange={handleLogoChange}
              disabled={isUploadingLogo || !!instituteData.institute?.logo}
            />
          </label>
        </div>

        {instituteData.institute?.logo && (
          <p className="text-success text-sm mt-2">
            <span className="inline-block w-2 h-2 bg-success rounded-full mr-1"></span>
            Logo uploaded successfully
          </p>
        )}

        <p className="text-sm text-base-content/70 mt-2">
          Logo is optional. You can proceed without uploading one.
        </p>
      </div>

      {!instituteData.institute ? (
        <div className="card bg-base-200 shadow-sm">
          <div className="card-body">
            <InputField
              type="text"
              label="Institute Name*"
              placeholder="Enter your institute name"
              name="name"
              errorMessage={formErrors.name?.message}
              register={register}
              required
            >
              <BuildingOfficeIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            </InputField>

            <InputField
              type="email"
              label="Email Address*"
              errorMessage={formErrors.email?.message}
              placeholder="<EMAIL>"
              name="email"
              required
              register={register}
            >
              <EnvelopeIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            </InputField>
          </div>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card bg-base-100 border border-success shadow-sm"
        >
          <div className="card-body">
            <div className="flex items-center gap-3 mb-2">
              <CheckCircleIcon className="h-6 w-6 text-success" />
              <h3 className="text-lg font-semibold">
                Institute Added Successfully
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-base-content/70">Institute Name</p>
                <p className="font-medium">{instituteData.institute.name}</p>
              </div>
              <div>
                <p className="text-sm text-base-content/70">Email Address</p>
                <p className="font-medium">{instituteData.institute.email}</p>
              </div>
              {instituteData.institute.id && (
                <div>
                  <p className="text-sm text-base-content/70">Institute ID</p>
                  <p className="font-medium">{instituteData.institute.id}</p>
                </div>
              )}
            </div>

            <div className="mt-3 flex items-center">
              <div className="badge badge-outline badge-success gap-1">
                <CheckCircleIcon className="h-3 w-3" />
                Ready to proceed
              </div>
            </div>
          </div>
        </motion.div>
      )}

      <div className="flex justify-center mt-2">
        <button
          className="btn btn-primary min-w-40"
          onClick={handleSubmit(onSubmit)}
          disabled={
            isInstituteLoading || isUploadingLogo || !!instituteData.institute
          }
        >
          {isInstituteLoading ? (
            <>
              <span className="loading loading-spinner loading-sm"></span>
              Adding Institute...
            </>
          ) : instituteData.institute ? (
            "Institute Added ✓"
          ) : (
            "Add Institute"
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default SetupInstitute;
