import { sendApiRequest } from "@/common/services/api.service";
import type {
  CreateInstitutePayload,
  Institute,
  UpdateInstitutePayload,
} from "./institute.type";

export interface GetOwnerInstituteResponse {
  institute: Institute | null;
}

export async function createInstitute(
  payload: CreateInstitutePayload,
): Promise<Institute> {
  return await sendApiRequest<Institute>("/institutes", {
    method: "POST",
    data: payload,
    withAuthorization: true,
  });
}

interface UpdateInstituteParams {
  payload: UpdateInstitutePayload;
}
export async function updateInstitute({
  payload,
}: UpdateInstituteParams): Promise<Institute> {
  return await sendApiRequest<Institute>(`/institutes/me`, {
    method: "PATCH",
    data: payload,
    withAuthorization: true,
  });
}

export async function getOwnerInstitute(): Promise<GetOwnerInstituteResponse> {
  return await sendApiRequest<GetOwnerInstituteResponse>(`/institutes/me`, {
    method: "GET",
    withAuthorization: true,
  });
}
