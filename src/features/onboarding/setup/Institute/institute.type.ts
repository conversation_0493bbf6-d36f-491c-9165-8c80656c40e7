import { z } from "zod";
import type { createInstituteSchema } from "./institute.schema";

export type CreateInstitutePayload = z.infer<typeof createInstituteSchema>;

export type UpdateInstitutePayload = Partial<
  Omit<Institute, "id" | "ownerId" | "createdAt">
>;

export interface Institute {
  id: string;
  name: string;
  email: string;
  logo: string;
  createdAt: string;
  ownerId: string;
  isActive: boolean;
  isBasicSetupComplete: boolean;
}
