import React, { type ChangeEvent, useEffect } from "react";
import { motion } from "framer-motion";
import {
  BuildingOfficeIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import { InputField } from "@/common/components/ui/form/InputField";
import { type SubmitHandler, useForm } from "react-hook-form";
import type { CreateBranchPayload } from "./branch.type";
import { zodResolver } from "@hookform/resolvers/zod";
import { createBranchSchema } from "./schema/branch.schema";
import {
  getUseBranchesQueryKey,
  useBranches,
  useCreateBranch,
} from "./branches-query";
import { ErrorComponent } from "@/common/components/ErrorComponent";
import { useInstitute } from "../Institute/institute-query";
import { notify } from "@/lib/notify";
import { useQueryClient } from "@tanstack/react-query";
import { showErrorNotification } from "@/utils/exception.utils";

interface BranchFormProps {
  setNextEnable: (enable: boolean) => void;
}

const BranchesSetup: React.FC<BranchFormProps> = ({ setNextEnable }) => {
  const queryClient = useQueryClient();

  const {
    data: { institute },
  } = useInstitute();

  const {
    data: branches,
    isFetching: isBranchesLoading,
    error: fetchBranchesError,
  } = useBranches(institute?.id);

  const createBranchMutation = useCreateBranch();

  const {
    register,
    handleSubmit,
    reset: resetForm,
    setValue: setFormValue,
    getValues: getFormFieldValues,
    formState: { errors: formErrors, isSubmitting: isFormSubmitting },
  } = useForm<CreateBranchPayload>({
    resolver: zodResolver(createBranchSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      address: "",
      phone: "",
      email: "",
    },
  });

  const onSubmit: SubmitHandler<CreateBranchPayload> = async (data) => {
    try {
      if (institute) {
        await createBranchMutation.mutateAsync({
          payload: data,
          instituteId: institute.id,
        });

        await queryClient.invalidateQueries({
          queryKey: getUseBranchesQueryKey(institute.id),
        });

        notify.success("Branch added successfully");
        resetForm();
      }
    } catch (error: unknown) {
      showErrorNotification(error);
    }
  };

  function handleIsMainBranchChange(e: ChangeEvent<HTMLInputElement>) {
    setFormValue("isMain", e.target.checked);
  }

  useEffect(() => {
    setNextEnable(Boolean(branches?.length));
  }, [branches, setNextEnable]);

  if (fetchBranchesError) {
    return <ErrorComponent error={fetchBranchesError} />;
  }

  if (!institute) {
    notify.error("Please create an institute first");
    return <ErrorComponent message="Please create an institute first" />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col items-center mb-4">
        <h2 className="text-2xl font-bold">{"Create Branch"}</h2>
        <p className="text-base-content/70 mt-2 text-center">
          Add a new branch to your institute
        </p>
      </div>

      <div className="card bg-base-200 shadow-sm">
        <div className="card-body">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <InputField
              type="text"
              label="Branch Name"
              errorMessage={formErrors.name?.message}
              placeholder="Enter branch name"
              register={register}
              name="name"
              required
            >
              <BuildingOfficeIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
            </InputField>

            <InputField
              label="Address*"
              errorMessage={formErrors.address?.message}
              placeholder="Enter branch address"
              name="address"
              register={register}
              required
            >
              <MapPinIcon className="absolute right-2 top-4 h-5 w-5 text-gray-500" />
            </InputField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                type="tel"
                label="Phone*"
                errorMessage={formErrors.phone?.message}
                placeholder="Enter phone number"
                name="phone"
                register={register}
                required
              >
                <PhoneIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
              </InputField>

              <InputField
                type="email"
                label="Email*"
                errorMessage={formErrors.email?.message}
                placeholder="<EMAIL>"
                name="email"
                register={register}
                required
              >
                <EnvelopeIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
              </InputField>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 mt-4">
              <div className="form-control">
                <label className="cursor-pointer label justify-start gap-3">
                  <span className="label-text font-medium">Main Branch</span>
                  <input
                    type="checkbox"
                    id="isMain"
                    name="isMain"
                    checked={getFormFieldValues("isMain")}
                    onChange={handleIsMainBranchChange}
                    className="toggle toggle-primary"
                  />
                </label>
                <p className="text-xs text-base-content/70 ml-1">
                  Set as the primary branch for this institute
                </p>
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="btn btn-primary min-w-40"
                disabled={isFormSubmitting}
              >
                {isFormSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Creating...
                  </>
                ) : (
                  "Add Branch"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Branches List Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mt-10"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold">Branch</h3>
        </div>

        {isBranchesLoading ? (
          <div className="flex justify-center py-8">
            <span className="loading loading-spinner loading-lg"></span>
          </div>
        ) : !branches ? (
          <div className="card bg-base-200 shadow-sm">
            <div className="card-body flex items-center justify-center py-12">
              <BuildingOfficeIcon className="h-16 w-16 text-base-content/20 mb-4" />
              <p className="text-center text-base-content/70">
                No branches found for this institute
              </p>
              <p className="text-center text-sm text-base-content/50 mt-1">
                Create your first branch above
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {branches.map((branch) => (
              <motion.div
                key={branch.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`card bg-base-100 shadow-sm border ${branch.isMain ? "border-primary" : "border-base-200"} hover:shadow-md transition-shadow`}
              >
                <div className="card-body p-4">
                  <div className="flex justify-between">
                    <div className="flex items-center gap-2">
                      <BuildingOfficeIcon
                        className={`h-5 w-5 ${branch.isMain ? "text-primary" : "text-base-content/70"}`}
                      />
                      <h3 className="font-medium">{branch.name}</h3>
                      {branch.isMain && (
                        <div className="badge badge-primary badge-sm">Main</div>
                      )}
                      {!branch.isActive && (
                        <div className="badge badge-ghost badge-sm">
                          Inactive
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2 text-sm">
                    <div>
                      <p className="text-xs text-base-content/70">Address</p>
                      <p className="truncate">{branch.address}</p>
                    </div>
                    <div>
                      <p className="text-xs text-base-content/70">Phone</p>
                      <p>{branch.phone}</p>
                    </div>
                    <div>
                      <p className="text-xs text-base-content/70">Email</p>
                      <p className="truncate">{branch.email}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};

export default BranchesSetup;
