import { sendApiRequest } from "@/common/services/api.service";
import type { Branch, CreateBranchPayload } from "../branch.type";
import { logger } from "@/lib/logger";

export async function fetchAllBranches(instituteId: string) {
  try {
    return await sendApiRequest<Branch[]>(
      `/institutes/${instituteId}/branches`,
      {
        method: "GET",
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Failed to fetch branches", error);
    throw error;
  }
}

export async function createBranch({
  instituteId,
  payload,
}: {
  instituteId: string;
  payload: CreateBranchPayload;
}) {
  try {
    return await sendApiRequest<Branch>(`/institutes/${instituteId}/branches`, {
      method: "POST",
      data: payload,
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.error("Failed to create branch", error);
    throw error;
  }
}
