import { useMutation, useQuery } from "@tanstack/react-query";
import { createBranch, fetchAllBranches } from "./service/branch.service";

export const getUseBranchesQueryKey = (instituteId?: string) => [
  "branches",
  instituteId,
];

export const useBranches = (instituteId?: string) => {
  return useQuery({
    queryKey: getUseBranchesQueryKey(instituteId),
    queryFn: async () => {
      return await fetchAllBranches(instituteId!);
    },
    enabled: <PERSON>olean(instituteId),
  });
};

export const useCreateBranch = () => {
  return useMutation({
    mutationFn: createBranch,
  });
};
