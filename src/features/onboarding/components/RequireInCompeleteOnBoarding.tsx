import { Navigate, Outlet, useLocation } from "react-router";
import { useInstitute } from "../setup/Institute/institute-query";
import { logger } from "@/lib/logger";
import { useUserProfile } from "@/core/user/user-query";
import { isInstituteOwner } from "@/utils/user-roles.utils";

export const RequireInCompleteOnboarding = () => {
  const { data: user } = useUserProfile();
  const isOwner = isInstituteOwner(user);
  const { data: instituteData } = useInstitute(isOwner);

  const location = useLocation();
  if (!isOwner || instituteData?.institute?.isBasicSetupComplete) {
    logger.log("Onboarding complete redirecting to dashboard");
    return <Navigate to="/" state={{ from: location }} />;
  }
  logger.log("Onboarding incomplete redirecting to onboarding");
  return <Outlet />;
};
