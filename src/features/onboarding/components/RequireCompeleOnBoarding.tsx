import { Navigate, Outlet, useLocation } from "react-router";
import { useInstitute } from "../setup/Institute/institute-query";
import { useUserProfile } from "@/core/user/user-query";
import { isInstituteOwner } from "@/utils/user-roles.utils";

export const RequireCompleteOnboarding = () => {
  const { data: user } = useUserProfile();

  const isOwner = isInstituteOwner(user);
  const { data: instituteData } = useInstitute(isOwner);

  const location = useLocation();

  if (isOwner && !instituteData?.institute?.isBasicSetupComplete) {
    return <Navigate to="/onboarding" state={{ from: location }} />;
  }
  return <Outlet />;
};
