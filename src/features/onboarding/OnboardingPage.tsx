import { useState } from "react";
import { motion } from "framer-motion";
import SetupInstitute from "./setup/Institute/InstituteSetup";
import BranchesSetup from "./setup/branches/BranchSetup";
import { logger } from "@/lib/logger";
import { useNavigate } from "react-router";
import {
  useInstitute,
  useUpdateInstitute,
} from "./setup/Institute/institute-query";

export const OnboardingPage = () => {
  const { data: instituteData } = useInstitute();

  const [currentStep, setCurrentStep] = useState<number>(1);
  const [nextEnabled, setNextEnabled] = useState<boolean>(false);
  const navigate = useNavigate();

  const updateInstituteMutation = useUpdateInstitute();

  const handleOnboardingComplete = async () => {
    if (!instituteData?.institute) {
      logger.error("Cannot complete onboarding. Institute not found");
      return;
    }

    try {
      await updateInstituteMutation.mutateAsync({
        payload: { isBasicSetupComplete: true },
      });
      await navigate("/");
    } catch (error) {
      logger.error(error);
    }
  };

  const handleNext = async () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    } else {
      await handleOnboardingComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setNextEnabled(true);
    }
  };

  return (
    <div className="flex-1 flex items-center justify-center bg-base-200 p-4">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl w-full bg-base-100 rounded-lg shadow-md overflow-hidden"
      >
        <div className="p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-primary">
              Welcome to E-Bridge
            </h1>
            <p className="text-base-content/70 mt-2">
              Let's set up your school management system
            </p>
          </div>

          {/* Stepper */}
          <ul className="steps steps-horizontal w-full">
            <li className={`step ${currentStep >= 1 ? "step-primary" : ""}`}>
              Institute
            </li>
            <li className={`step ${currentStep >= 2 ? "step-primary" : ""}`}>
              Branches
            </li>
          </ul>

          {/* Step Content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="p-6 rounded-lg"
          >
            {currentStep === 1 && (
              <SetupInstitute setNextEnable={setNextEnabled} />
            )}
            {currentStep === 2 && (
              <BranchesSetup setNextEnable={setNextEnabled} />
            )}
          </motion.div>

          {/* Navigation */}
          <div className="flex justify-between">
            <button
              className="btn btn-secondary"
              onClick={handleBack}
              disabled={currentStep === 1}
            >
              Back
            </button>

            <button
              className="btn btn-primary"
              onClick={handleNext}
              disabled={!nextEnabled}
            >
              {currentStep === 2 ? "Complete" : "Next"}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
