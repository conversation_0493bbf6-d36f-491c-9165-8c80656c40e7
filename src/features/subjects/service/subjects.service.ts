import type {
  CreateSubjectPayload,
  Subject,
  UpdateSubjectPayload,
} from "../types/subjects.type";
import { logger } from "@/lib/logger";
import { sendApiRequest } from "@/common/services/api.service";
import { config } from "@/config/config";
import type {
  PaginatedApiResponse,
  PaginationParams,
} from "@/common/types/global.types";

export async function createSubject({
  sessionId,
  payload,
}: {
  sessionId: string;
  payload: CreateSubjectPayload;
}) {
  try {
    return await sendApiRequest(config.endpoint.subjects.add(sessionId), {
      method: "POST",
      withAuthorization: true,
      data: payload,
    });
  } catch (error: unknown) {
    logger.error("Error creating subject", error);
    throw error;
  }
}

export async function updateSubject({
  sessionId,
  subjectId,
  payload,
}: {
  sessionId: string;
  subjectId: string;
  payload: UpdateSubjectPayload;
}) {
  try {
    return await sendApiRequest(
      config.endpoint.subjects.update(sessionId, subjectId),
      {
        method: "PATCH",
        withAuthorization: true,
        data: payload,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating subject", error);
    throw error;
  }
}

export async function fetchAllSubjects(
  sessionId: string,
  paginationParams?: PaginationParams,
): Promise<PaginatedApiResponse<Subject>> {
  try {
    return await sendApiRequest<PaginatedApiResponse<Subject>>(
      config.endpoint.subjects.list(sessionId),
      {
        method: "GET",
        withAuthorization: true,
        params: paginationParams,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching subjects", error);
    throw error;
  }
}
