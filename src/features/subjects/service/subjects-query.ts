import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createSubject,
  fetchAllSubjects,
  updateSubject,
} from "./subjects.service";
import { notify } from "@/lib/notify";
import { showErrorNotification } from "@/utils/exception.utils";
import type { PaginationParams } from "@/common/types/global.types";

const getSubjectsQueryKey = (
  sessionId?: string,
  paginationParams?: PaginationParams,
) =>
  paginationParams
    ? ["subjects", sessionId, paginationParams.offset, paginationParams.limit]
    : ["subjects", sessionId];

export const useSubjects = (
  sessionId?: string,
  paginationParams?: PaginationParams,
) => {
  return useQuery({
    queryKey: getSubjectsQueryKey(sessionId, paginationParams),
    queryFn: async () => {
      return await fetchAllSubjects(sessionId!, paginationParams);
    },
    enabled: <PERSON><PERSON><PERSON>(sessionId),
  });
};

export const useCreateSubject = (sessionId?: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createSubject,
    onSuccess: async () => {
      notify.success("Subject added successfully");
      await queryClient.invalidateQueries({
        queryKey: getSubjectsQueryKey(sessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
};

export const useUpdateSubject = () => {
  return useMutation({
    mutationFn: updateSubject,
  });
};
