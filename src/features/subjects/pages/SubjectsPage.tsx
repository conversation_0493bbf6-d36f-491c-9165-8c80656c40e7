import { useState, useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { InputField } from "@/common/components/ui/form/InputField";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import { RequiresResource } from "@/common/components/RequiresResourceCard";
import {
  createSubjectSchema,
  updateSubjectSchema,
} from "../schema/subjects.schema";
import {
  useCreateSubject,
  useSubjects,
  useUpdateSubject,
} from "../service/subjects-query";
import type {
  Subject,
  CreateSubjectPayload,
  UpdateSubjectPayload,
} from "../types/subjects.type";
import { notify } from "@/lib/notify";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { formatDateInLocalFormat } from "@/utils/utils";
import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";

export const SubjectsPage = () => {
  // State for pagination
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  // State for form mode and view modal
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Get active academic session
  const { activeAcademicSession } = useAcademicSessionStore();

  // Fetch subjects with pagination
  const {
    data: subjectsResponse,
    isLoading,
    refetch,
  } = useSubjects(activeAcademicSession?.id, paginationParams);

  // Mutations for create and update
  const createSubjectMutation = useCreateSubject(activeAcademicSession?.id);
  const updateSubjectMutation = useUpdateSubject();

  // Determine which schema to use based on mode
  const schema = isEditMode ? updateSubjectSchema : createSubjectSchema;
  type FormData = z.infer<typeof schema>;

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      type: "THEORY",
      marks: 100,
    },
  });

  // Reset form when switching between add/edit modes
  useEffect(() => {
    if (isEditMode && selectedSubject) {
      reset(selectedSubject);
    } else {
      reset({
        name: "",
        type: "THEORY",
        marks: 0,
      });
    }
  }, [isEditMode, selectedSubject, reset]);

  // Handle form submission
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (!activeAcademicSession) {
      notify.error("No active academic session found");
      return;
    }

    try {
      if (isEditMode && selectedSubject) {
        await updateSubjectMutation.mutateAsync({
          sessionId: activeAcademicSession.id,
          subjectId: selectedSubject.id,
          payload: data as UpdateSubjectPayload,
        });
      } else {
        await createSubjectMutation.mutateAsync({
          sessionId: activeAcademicSession.id,
          payload: data as CreateSubjectPayload,
        });
      }

      // Reset form and refresh data
      cancelEdit();
      void refetch();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleEdit = (subject: Subject) => {
    setSelectedSubject(subject);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedSubject(null);
    reset({
      name: "",
      type: "THEORY",
      marks: 100,
    });
  };

  // If no active academic session, show message
  if (!activeAcademicSession) {
    return (
      <RequiresResource resourceName="Academic Session" createUrl="/sessions" />
    );
  }

  return (
    <div className="space-y-8">
      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={isEditMode}
        onFormSubmit={handleSubmit(onSubmit)}
        onCancelEdit={cancelEdit}
        resourceLabel="Subject"
      >
        <InputField
          placeholder="Mathematics"
          name="name"
          label="Subject Name"
          register={register}
          errorMessage={fieldErrors.name?.message}
        />

        <SelectField
          register={register}
          name="type"
          label="Subject Type"
          errorMessage={fieldErrors.type?.message}
          options={[
            { value: "THEORY", label: "Theory" },
            { value: "PRACTICAL", label: "Practical" },
          ]}
        />

        <InputField
          placeholder="100"
          name="marks"
          label="Total Marks"
          type="number"
          valueAsNumber
          register={register}
          errorMessage={fieldErrors.marks?.message}
        />
      </ResourceCreationForm>

      <ResourceListingTable
        records={subjectsResponse?.items ?? []}
        totalRecords={subjectsResponse?.total ?? 0}
        isDataLoading={isLoading}
        searchLabel="name"
        searchFieldValue={(record) => record.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Name", render: (s) => s.name },
          { label: "Type", render: (s) => s.type },
          { label: "Marks", render: (s) => s.marks },
        ]}
        onView={(item) => {
          setSelectedSubject(item);
          setIsViewModalOpen(true);
        }}
      />

      {/* View Subject Modal */}
      {isViewModalOpen && selectedSubject && (
        <ViewRecordModal
          title="Subject Details"
          subtitle={selectedSubject.name}
          record={selectedSubject}
          columns={[
            { label: "Name", render: (s) => s.name },
            {
              label: "Type",
              render: (s) => (s.type === "THEORY" ? "Theory" : "Practical"),
            },
            { label: "Marks", render: (s) => s.marks },
            {
              label: "Created At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedSubject);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedSubject(null);
          }}
        />
      )}
    </div>
  );
};
