import { z } from "zod";
import { createClassSchema, updateClassSchema } from "../schema/class.schema";

export type CreateClassPayload = z.infer<typeof createClassSchema>;

export type UpdateClassPayload = z.infer<typeof updateClassSchema>;

export interface Class extends Omit<CreateClassPayload, "sections"> {
  id: string;
  sections: {
    id: string;
    name: string;
    classTeacher: {
      id: string;
      name: string;
    };
  }[];
  createdAt: string;
}

export interface ClassSection {
  id: string;
  name: string;
  class: {
    id: string;
    name: string;
  };
  isActive: boolean;
  createdAt: Date;
  totalStudents: number;
  classTeacher: {
    id: string;
    name: string;
  };
}
