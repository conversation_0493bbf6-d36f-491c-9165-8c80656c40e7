import { sendApiRequest } from "@/common/services/api.service";
import type {
  Class,
  CreateClassPayload,
  UpdateClassPayload,
} from "../types/class.type";
import { logger } from "@/lib/logger";
import type { PaginatedApiResponse } from "@/common/types/global.types";

type CreateClassParams = {
  academicSessionId: string;
  payload: CreateClassPayload;
};

export async function createClass({
  payload,
  academicSessionId,
}: CreateClassParams): Promise<void> {
  try {
    await sendApiRequest(`/academic-sessions/${academicSessionId}/classes`, {
      method: "POST",
      data: payload,
      withAuthorization: true,
    });
  } catch (error: unknown) {
    logger.error("Error creating class", error);
    throw error;
  }
}

type UpdateClassParams = {
  academicSessionId: string;
  classId: string;
  payload: UpdateClassPayload;
};

export async function updateClass({
  academicSessionId,
  classId,
  payload,
}: UpdateClassParams): Promise<void> {
  try {
    await sendApiRequest(
      `/academic-sessions/${academicSessionId}/classes/${classId}`,
      {
        method: "PATCH",
        data: payload,
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error updating class", error);
    throw error;
  }
}

export async function fetchAllClasses(academicSessionId: string) {
  try {
    return await sendApiRequest<PaginatedApiResponse<Class>>(
      `/academic-sessions/${academicSessionId}/classes`,
      {
        method: "GET",
        withAuthorization: true,
      },
    );
  } catch (error: unknown) {
    logger.error("Error fetching classes", error);
    throw error;
  }
}
