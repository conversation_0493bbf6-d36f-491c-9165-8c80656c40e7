import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { showErrorNotification } from "@/utils/exception.utils";
import { notifyResourceActionSuccess } from "@/utils/notifications.utils";
import { createClass, fetchAllClasses, updateClass } from "./classes.service";

const getClassesQueryKey = (academicSessionId?: string) => [
  "classes",
  academicSessionId,
];

export function useCreateClass(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createClass,
    onSuccess: async () => {
      notifyResourceActionSuccess("Class", "create");
      await queryClient.invalidateQueries({
        queryKey: getClassesQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}

export function useClasses(academicSessionId?: string) {
  return useQuery({
    queryKey: getClasses<PERSON><PERSON>y<PERSON>ey(academicSessionId),
    queryFn: async () => {
      if (academicSessionId) {
        return await fetchAllClasses(academicSessionId);
      }
    },
    enabled: Boolean(academicSessionId),
  });
}

export function useUpdateClass(academicSessionId?: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateClass,
    onSuccess: async () => {
      notifyResourceActionSuccess("Class", "update");
      await queryClient.invalidateQueries({
        queryKey: getClassesQueryKey(academicSessionId),
      });
    },
    onError: (error) => {
      showErrorNotification(error);
    },
  });
}
