import { Button } from "@/common/components/ui/Button";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";
import type { Class } from "../../types/class.type";
import { formatIndianNumber } from "@/utils/CommaSeparator";

export interface ViewClassModalProps {
  classData: Class;
  onClose: () => void;
  onUpdate: () => void;
}

const ViewClassModal = ({
  classData,
  onClose,
  onUpdate,
}: ViewClassModalProps) => {
  return (
    <div className="fixed inset-0 bg-semi-transparent bg-opacity-60 flex justify-center items-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-base-100 rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative"
      >
        <button
          className="absolute cursor-pointer top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
          onClick={onClose}
        >
          <XMarkIcon className="h-6 w-6" />
        </button>

        <div className="p-6">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mt-4">{classData.name}</h2>
            <p className="text-sm text-gray-500">
              Class Teacher • {classData.classTeacher.name}
            </p>
          </div>

          <div className="divider"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="font-semibold text-lg">Class Information</h3>
              <p>
                <span className="font-medium">Maximum Students:</span>{" "}
                {classData.maximumStudents}
              </p>
              <p>
                <span className="font-medium">Fee Per Month:</span>{" "}
                {formatIndianNumber(classData.feePerMonth)}
              </p>
              <p>
                <span className="font-medium">Created At:</span>{" "}
                {new Date(classData.createdAt).toLocaleDateString()}
              </p>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold text-lg">Sections</h3>
              <div className="flex flex-wrap gap-2">
                {classData.sections.map((section, index) => (
                  <span key={index} className="badge badge-primary badge-lg">
                    {section.name}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-center">
            <Button shape="primary" onClick={onUpdate} className="px-6">
              Update
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ViewClassModal;
