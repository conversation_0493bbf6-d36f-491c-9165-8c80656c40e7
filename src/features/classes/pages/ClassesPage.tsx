import { ResourceListingTable } from "@/common/components/ResourceListingTable";
import { ViewRecordModal } from "@/common/components/VIewRecordModal";
import { apiParams } from "@/common/constants/api-params.constant";
import type { PaginationParams } from "@/common/types/global.types";
import { formatDateInLocalFormat } from "@/utils/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { type SubmitHandler, useFieldArray, useForm } from "react-hook-form";
import type { Class } from "../types/class.type";
import { createClassSchema, updateClassSchema } from "../schema/class.schema";
import { ResourceCreationForm } from "@/common/components/ResourceCreationForm";
import { InputField } from "@/common/components/ui/form/InputField";
import { z } from "zod";
import { useAcademicSessionStore } from "@/features/academic-session/session.store";
import {
  useClasses,
  useCreateClass,
  useUpdateClass,
} from "../service/class-query";
import { logger } from "@/lib/logger";
import { Button } from "@/common/components/ui/Button";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { Trash2 } from "lucide-react";
import { useStaffByType } from "@/features/staff/services/staff-query";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";

export function ClassesPage() {
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    offset: apiParams.pagination.OFFSET,
    limit: apiParams.pagination.LIMIT,
  });

  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const { activeAcademicSession } = useAcademicSessionStore();
  const { data: classesData, isFetching } = useClasses(
    activeAcademicSession?.id,
  );

  const { selectedBranch } = useBranchStore();
  const { data: staffDate } = useStaffByType("TEACHER", selectedBranch?.id);
  const createClassMutation = useCreateClass(activeAcademicSession?.id);
  const updateClassMutation = useUpdateClass(activeAcademicSession?.id);

  // Form setup
  const schema = isEditMode ? updateClassSchema : createClassSchema;
  type FormData = z.infer<typeof schema>;

  const formDefaultValue = {
    name: "",
    maximumStudents: 0,
    feePerMonth: 0,
    sections: [{ name: "", classTeacherId: "" }],
  };
  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors: fieldErrors, isSubmitting },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: formDefaultValue,
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "sections",
  });

  const handleEdit = (selectedClass: Class) => {
    setSelectedClass(selectedClass);
    setIsEditMode(true);
  };

  const cancelEdit = () => {
    setIsEditMode(false);
    setSelectedClass(null);
    reset(formDefaultValue);
  };

  const onSubmit: SubmitHandler<FormData> = (data) => {
    if (!activeAcademicSession) {
      logger.error("Cannot create class. No active academic session");
      return;
    }
    if (isEditMode && selectedClass) {
      updateClassMutation.mutate({
        academicSessionId: activeAcademicSession.id,
        classId: selectedClass.id,
        payload: data,
      });
    } else {
      createClassMutation.mutate({
        academicSessionId: activeAcademicSession.id,
        payload: data,
      });
    }

    // Reset form and refresh data
    reset();
    cancelEdit();
  };

  useEffect(() => {
    if (isEditMode && selectedClass) {
      reset({
        ...selectedClass,
        sections: selectedClass.sections.map((section) => ({
          id: section.id,
          name: section.name,
          classTeacherId: section.classTeacher.id,
        })),
      });
    }
  }, [isEditMode, selectedClass, reset]);
  return (
    <div className="space-y-8">
      <ResourceCreationForm
        isFormSubmitting={isSubmitting}
        isEditMode={isEditMode}
        onFormSubmit={handleSubmit(onSubmit)}
        onCancelEdit={cancelEdit}
        resourceLabel="Class"
      >
        <InputField
          placeholder="Grade 1"
          name="name"
          label="Name"
          register={register}
          errorMessage={fieldErrors.name?.message}
        />

        <InputField
          placeholder="30"
          name="maximumStudents"
          label="Maximum Students"
          register={register}
          errorMessage={fieldErrors.maximumStudents?.message}
          type="number"
          valueAsNumber
        />

        <InputField
          placeholder="5000.00"
          name="feePerMonth"
          label="Fee (in PKR)"
          register={register}
          errorMessage={fieldErrors.feePerMonth?.message}
          type="number"
          valueAsNumber
        />

        <div>
          <fieldset className="space-y-2">
            <legend className="font-bold">Sections</legend>
            {fields.map((field, index) => (
              <div key={field.id} className="flex items-center gap-2">
                <InputField
                  placeholder={`Section ${index + 1}`}
                  name={`sections.${index}.name`}
                  label="Name"
                  register={register}
                  errorMessage={fieldErrors.sections?.[index]?.name?.message}
                />

                <SelectField
                  register={register}
                  name={`sections.${index}.classTeacherId`}
                  label="Class Teacher"
                  defaultOptionLabel="-- Select Class Teacher --"
                  errorMessage={
                    fieldErrors.sections?.[index]?.classTeacherId?.message
                  }
                  options={staffDate?.items.map((staff) => ({
                    value: staff.id,
                    label: staff.name,
                  }))}
                />

                {!isEditMode && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      remove(index);
                    }}
                    className="text-red-500 pt-6 ml-4 cursor-pointer"
                  >
                    <Trash2 />
                  </button>
                )}
              </div>
            ))}
            {fieldErrors.sections && (
              <p className="text-red-500">{fieldErrors.sections.message}</p>
            )}
          </fieldset>

          <Button
            type="button"
            onClick={() => {
              append({ name: "", classTeacherId: "" });
            }}
            className="mt-4"
          >
            Add Section
          </Button>
        </div>
      </ResourceCreationForm>

      <ResourceListingTable
        records={classesData?.items}
        totalRecords={classesData?.total}
        isDataLoading={isFetching}
        searchLabel="name"
        searchFieldValue={(record) => record.name}
        pagination={paginationParams}
        onPaginationChange={setPaginationParams}
        columns={[
          { label: "Name", render: (s) => s.name },
          { label: "Max Students", render: (s) => s.maximumStudents },
          { label: "Fee Per Month", render: (s) => s.feePerMonth },
          {
            label: "Sections",
            render: (s) => s.sections.map((section) => section.name).join(", "),
          },
        ]}
        onView={(item) => {
          setSelectedClass(item);
          setIsViewModalOpen(true);
        }}
      />

      {/* View Class Modal */}
      {isViewModalOpen && selectedClass && (
        <ViewRecordModal
          title="Class Details"
          subtitle={selectedClass.name}
          record={selectedClass}
          columns={[
            { label: "Name", render: (s) => s.name },
            {
              label: "Max Students",
              render: (s) => s.maximumStudents,
            },
            { label: "Fee Per Month", render: (s) => s.feePerMonth },
            {
              label: "Sections",
              render: (s) =>
                s.sections.map((section) => section.name).join(", "),
            },
            {
              label: "Created At",
              render: (s) => formatDateInLocalFormat(s.createdAt),
            },
          ]}
          onUpdate={() => {
            setIsViewModalOpen(false);
            handleEdit(selectedClass);
          }}
          onClose={() => {
            setIsViewModalOpen(false);
            setSelectedClass(null);
          }}
        />
      )}
    </div>
  );
}
