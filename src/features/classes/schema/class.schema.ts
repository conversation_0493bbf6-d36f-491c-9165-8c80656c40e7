import { z } from "zod";

const sectionSchema = z.object({
  name: z.string().min(1, "Section name is required"),
  classTeacherId: z.string().nonempty("Class teacher is required"),
});

const classBaseSchema = z.object({
  name: z.string().min(1, "Class name is required"),
  sections: z
    .array(sectionSchema)
    .min(1, { message: "At least one section is required" }),
  maximumStudents: z
    .number()
    .min(1, "Maximum students is required")
    .max(100, "Maximum students must be less than 100"),
  feePerMonth: z.number().min(1, "Fee is required"),
});

export const createClassSchema = classBaseSchema;

export const updateClassSchema = classBaseSchema
  .omit({ sections: true })
  .extend({
    sections: z.array(sectionSchema.extend({ id: z.string().optional() })),
  });
