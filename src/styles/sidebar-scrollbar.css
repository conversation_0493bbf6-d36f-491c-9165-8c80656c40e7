/* Custom scrollbar styling for sidebar */
.sidebar-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* Add padding to compensate for scrollbar width to prevent content shift */
  padding-right: 4px;
  /* Ensure the width is calculated including the scrollbar to prevent layout shifts */
  box-sizing: content-box;
  width: 256px; /* 64 * 4px = 256px, matching the w-64 class */
}

.sidebar-scrollbar::-webkit-scrollbar {
  width: 0;
  display: none; /* Chrome, Safari, Opera */
}

.sidebar-scrollbar:hover {
  scrollbar-width: thin; /* Firefox */
  -ms-overflow-style: auto; /* IE and Edge */
}

.sidebar-scrollbar:hover::-webkit-scrollbar {
  width: 4px;
  display: block;
}

.sidebar-scrollbar:hover::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.sidebar-scrollbar:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Prevent content shift when scrollbar appears */
.sidebar-content {
  width: calc(100% - 4px); /* Subtract scrollbar width */
}
