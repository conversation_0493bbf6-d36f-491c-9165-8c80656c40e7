@import "tailwindcss";
@import "./sidebar-scrollbar.css";

@plugin "daisyui" {
  themes:
    corporate --default,
    business --prefersdark;
}

@theme {
  --color-midnight: #121063;
  --color-tahiti: #3ab7bf;
  --color-bermuda: #78dcca;
  --color-semi-transparent: #00000029;
  --color-border-primary: rgb(228, 228, 231);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* For Webkit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(#0082a4);
  border-radius: 10px;
  border: none;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(#005f7f);
}

/* Target and hide all scrollbar buttons */
::-webkit-scrollbar-button {
  display: none !important;
  width: 0;
  height: 0;
}

::-webkit-scrollbar-button:start:decrement,
::-webkit-scrollbar-button:end:increment {
  display: none !important;
  height: 0;
  width: 0;
}
